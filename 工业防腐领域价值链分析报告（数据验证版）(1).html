<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二维聚合物材料涂料/工业防腐领域价值链分析报告（数据验证版）</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.js"></script>
    <style>
        /* CSS Reset */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* CSS Variables */
        :root {
            /* 主题色彩 */
            --primary-color: #1e40af;
            --primary-hover: #1d4ed8;
            --secondary-color: #3b82f6;
            --accent-color: #4f46e5;

            /* 背景色彩 */
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #e2e8f0;
            --bg-body: #f3f4f6;
            --bg-gray-light: #f9fafb;

            /* 文字色彩 */
            --text-primary: #1f2937;
            --text-secondary: #374151;
            --text-tertiary: #4b5563;
            --text-light: #ffffff;

            /* 边框色彩 */
            --border-primary: #e2e8f0;
            --border-secondary: #e5e7eb;

            /* 状态色彩 */
            --color-success: #059669;
            --color-warning: #d97706;
            --color-danger: #dc2626;
            --color-info: #0891b2;

            /* 渐变背景 */
            --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);

            /* 间距系统 */
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 0.75rem;
            --spacing-lg: 1rem;
            --spacing-xl: 1.5rem;
            --spacing-2xl: 2rem;
            --spacing-3xl: 3rem;

            /* 圆角 */
            --radius-sm: 0.25rem;
            --radius-md: 0.375rem;
            --radius-lg: 0.5rem;
            --radius-xl: 0.75rem;

            /* 阴影 */
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 10px 25px rgba(0, 0, 0, 0.1);

            /* 字体大小 */
            --font-size-xs: 0.75rem;
            --font-size-sm: 0.875rem;
            --font-size-base: 1rem;
            --font-size-lg: 1.125rem;
            --font-size-xl: 1.25rem;
            --font-size-2xl: 1.5rem;
            --font-size-3xl: 1.875rem;
            --font-size-4xl: 2.25rem;

            /* 过渡动画 */
            --transition-fast: all 0.2s ease;
            --transition-medium: all 0.3s ease;

            /* 布局 */
            --max-width: 80rem;
        }

        /* Base styles */
        body {
            background-color: var(--bg-body);
            min-height: 100vh;
            color: var(--text-secondary);
            line-height: 1.5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }

        /* Utility classes */
        .container {
            max-width: var(--max-width);
            margin: 0 auto;
            padding: 0 var(--spacing-lg);
        }

        .text-center {
            text-align: center;
        }

        .flex-center {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .flex-items-center {
            display: flex;
            align-items: center;
        }

        .shadow-sm {
            box-shadow: var(--shadow-sm);
        }

        .shadow-md {
            box-shadow: var(--shadow-md);
        }

        .shadow-lg {
            box-shadow: var(--shadow-lg);
        }

        .shadow-xl {
            box-shadow: var(--shadow-xl);
        }

        /* Typography utilities */
        .text-sm {
            font-size: var(--font-size-sm);
        }

        .text-base {
            font-size: var(--font-size-base);
        }

        .text-lg {
            font-size: var(--font-size-lg);
        }

        .text-xl {
            font-size: var(--font-size-xl);
        }

        .text-2xl {
            font-size: var(--font-size-2xl);
        }

        .text-3xl {
            font-size: var(--font-size-3xl);
        }

        .font-normal {
            font-weight: 400;
        }

        .font-medium {
            font-weight: 500;
        }

        .font-semibold {
            font-weight: 600;
        }

        .font-bold {
            font-weight: 700;
        }

        /* Header styles */
        header {
            background: var(--primary-color);
            color: var(--text-light);
        }

        .header-container {
            padding: var(--spacing-3xl) var(--spacing-lg);
        }

        .header-icon {
            font-size: var(--spacing-3xl);
            opacity: 0.8;
            margin-bottom: var(--spacing-lg);
        }

        .header-title {
            font-size: var(--font-size-4xl);
            font-weight: bold;
            margin-bottom: var(--spacing-md);
        }

        .header-subtitle {
            font-size: var(--font-size-xl);
            font-weight: 300;
            margin-bottom: var(--spacing-lg);
            opacity: 0.9;
        }

        .header-meta {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: var(--spacing-xl);
            font-size: var(--font-size-xs);
            opacity: 0.8;
        }

        .header-meta-item {
            gap: var(--spacing-xs);
        }

        /* Navigation styles */
        nav {
            background-color: var(--bg-primary);
            box-shadow: var(--shadow-md);
            position: sticky;
            top: 0;
            z-index: 50;
        }

        .nav-list {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: var(--spacing-xl);
            list-style: none;
            padding: var(--spacing-md) 0;
        }

        .nav-link {
            color: var(--text-tertiary);
            text-decoration: none;
            font-weight: 500;
            font-size: var(--font-size-sm);
            transition: var(--transition-fast);
        }

        .nav-link:hover {
            color: var(--accent-color);
        }

        /* Main container */
        .main-container {
            padding: var(--spacing-2xl) var(--spacing-lg);
        }

        /* Section styles */
        section {
            margin-bottom: var(--spacing-3xl);
        }

        .section-header {
            margin-bottom: var(--spacing-2xl);
        }

        .section-title {
            font-size: var(--font-size-3xl);
            font-weight: bold;
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
        }

        .section-description {
            font-size: var(--font-size-base);
            color: var(--text-tertiary);
            max-width: 48rem;
            margin: 0 auto;
        }

        /* Card component */
        .card {
            background-color: var(--bg-primary);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-xl);
            margin-bottom: var(--spacing-2xl);
        }

        .card-title {
            font-size: var(--font-size-xl);
            font-weight: bold;
            color: var(--text-primary);
            margin-bottom: var(--spacing-lg);
            gap: var(--spacing-sm);
        }

        /* Grid system */
        .grid {
            display: grid;
            gap: var(--spacing-xl);
        }

        .grid-cols-1 {
            grid-template-columns: repeat(1, minmax(0, 1fr));
        }

        @media (min-width: 768px) {
            .grid-cols-md-2 {
                grid-template-columns: repeat(2, minmax(0, 1fr));
            }
        }

        @media (min-width: 1024px) {
            .grid-cols-lg-4 {
                grid-template-columns: repeat(4, minmax(0, 1fr));
            }
        }

        /* Metric cards */
        .metric-card-wrapper {
            background-color: var(--bg-primary);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-xl);
            text-align: center;
        }

        .metric-icon-wrapper {
            width: 4rem;
            height: 4rem;
            border-radius: var(--radius-lg);
            margin: 0 auto var(--spacing-lg);
            color: var(--text-light);
        }

        .metric-icon {
            font-size: var(--font-size-xl);
        }

        .metric-value {
            font-size: var(--font-size-xl);
            font-weight: bold;
            color: var(--text-primary);
        }

        .metric-label {
            font-size: var(--font-size-sm);
            color: var(--text-tertiary);
        }

        /* Metric card variants */
        .metric-card-danger {
            background: var(--color-danger);
        }

        .metric-card-info {
            background: var(--color-info);
        }

        .metric-card-success {
            background: var(--color-success);
        }

        .metric-card-warning {
            background: #ea580c;
        }

        /* Box components */
        .box {
            padding: var(--spacing-xl);
            border-radius: var(--radius-lg);
            margin-bottom: var(--spacing-xl);
        }

        .box-insight {
            background-color: #eef2ff;
            border-left: 4px solid #6366f1;
        }

        .box-highlight {
            background-color: #fef3c7;
            border-left: 4px solid #f59e0b;
        }

        .box-title {
            font-size: var(--font-size-xl);
            font-weight: bold;
            margin-bottom: var(--spacing-md);
        }

        .box-title-insight {
            color: #4338ca;
        }

        .box-title-highlight {
            color: var(--color-warning);
        }

        .box-text {
            color: var(--text-secondary);
            line-height: 1.75;
        }

        /* Table component */
        .data-table {
            width: 100%;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        .data-table table {
            width: 100%;
            border-collapse: collapse;
            margin: var(--spacing-xl) 0;
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-md);
        }

        .data-table thead {
            background: var(--gradient-primary);
            color: var(--text-light);
        }

        .data-table th,
        .data-table td {
            padding: var(--spacing-md);
            text-align: left;
            font-size: var(--font-size-sm);
        }

        .data-table th {
            font-weight: 600;
            white-space: nowrap;
        }

        .data-table td {
            border-bottom: 1px solid var(--border-primary);
            color: var(--text-primary);
        }

        .data-table tbody tr:last-child td {
            border-bottom: none;
        }

        .data-table tbody tr:hover {
            background: var(--bg-secondary);
            transition: var(--transition-fast);
        }

        .data-table td:first-child {
            font-weight: 600;
            color: var(--primary-color);
        }

        /* Badge component */
        .badge {
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-size: var(--font-size-xs);
            font-weight: 500;
            display: inline-block;
        }

        .badge-warning {
            background-color: #fef3c7;
            color: var(--color-warning);
        }

        .badge-success {
            background-color: #d1fae5;
            color: var(--color-success);
        }


        /* Footer */
        footer {
            padding: var(--spacing-2xl) 0;
            margin-top: var(--spacing-3xl);
            border-top: 1px solid var(--border-secondary);
        }

        .footer-title {
            font-size: var(--font-size-lg);
            font-weight: bold;
            color: var(--text-primary);
            margin-bottom: var(--spacing-lg);
        }

        /* Reference list */
        .reference-list {
            font-size: var(--font-size-sm);
            color: var(--text-tertiary);
            list-style: none;
            counter-reset: reference-counter;
            padding: var(--spacing-lg);
            background-color: var(--bg-gray-light);
            border-left: 3px solid var(--border-secondary);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
        }

        .reference-list li {
            padding: var(--spacing-md) var(--spacing-lg) var(--spacing-md) var(--spacing-3xl);
            position: relative;
            transition: var(--transition-fast);
            counter-increment: reference-counter;
        }

        .reference-list li:hover {
            border-radius: var(--radius-md);
            background-color: #eeeeef;
            box-shadow: var(--shadow-sm);
        }

        .reference-list li::before {
            content: counter(reference-counter);
            position: absolute;
            left: var(--spacing-md);
            top: 50%;
            transform: translateY(-50%);
            width: 1.2rem;
            height: 1.2rem;
            color: var(--secondary-color);
            border: 1px solid var(--secondary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.6rem;
        }

        .reference-list a {
            color: #2563eb;
            text-decoration: none;
            word-break: break-word;
            font-weight: 500;
        }

        .reference-list a:hover {
            text-decoration: underline;
            color: var(--primary-hover);
        }

        /* Scrollbar styles */
        .custom-scrollbar::-webkit-scrollbar {
            height: 8px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
            background: var(--bg-tertiary);
            border-radius: var(--radius-sm);
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: var(--radius-sm);
        }

        /* Additional custom styles */
        .content-section {
            margin-bottom: var(--spacing-2xl);
        }

        .content-section h3 {
            color: var(--text-primary);
            font-size: var(--font-size-2xl);
            margin-bottom: var(--spacing-lg);
        }

        .content-section h4 {
            color: var(--text-primary);
            font-size: var(--font-size-xl);
            margin-bottom: var(--spacing-md);
            margin-top: var(--spacing-xl);
        }

        .content-section p {
            color: var(--text-secondary);
            line-height: 1.75;
            margin-bottom: var(--spacing-md);
        }

        .content-section ul,
        .content-section ol {
            margin: var(--spacing-md) 0 var(--spacing-lg) var(--spacing-xl);
        }

        .content-section li {
            color: var(--text-secondary);
            margin-bottom: var(--spacing-sm);
        }

        /* Responsive adjustments */
        @media (max-width: 640px) {
            :root {
                --spacing-lg: 0.75rem;
                --spacing-xl: 1rem;
                --spacing-2xl: 1.5rem;
                --spacing-3xl: 2rem;
            }

            .header-title {
                font-size: var(--font-size-2xl);
            }

            .header-subtitle {
                font-size: var(--font-size-base);
            }

            .section-title {
                font-size: var(--font-size-2xl);
            }

            .nav-list {
                gap: var(--spacing-md);
                font-size: var(--font-size-xs);
            }

            .reference-list {
                padding: var(--spacing-md);
            }

            .reference-list li {
                padding-left: var(--spacing-2xl);
            }
        }

        /* Chart container styles */
        .chart-container {
            position: relative;
            height: 400px;
            margin: var(--spacing-xl) 0;
        }

        .chart-wrapper {
            background: var(--bg-primary);
            padding: var(--spacing-xl);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            margin-bottom: var(--spacing-xl);
        }

        .chart-title {
            font-size: var(--font-size-lg);
            font-weight: bold;
            color: var(--text-primary);
            margin-bottom: var(--spacing-lg);
            text-align: center;
        }

        @media (max-width: 768px) {
            .chart-container {
                height: 300px;
            }
        }

        /* 市场结构列表样式 */
        .content-section ul {
            list-style: none;
            padding: var(--spacing-lg);
            margin: var(--spacing-lg) 0;
            border-radius: var(--radius-lg);
            background: #E3F2FD;
            position: relative;
            transition: var(--transition-medium);
        }

        .content-section ul:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .content-section>ul>li {
            margin-bottom: var(--spacing-md);
            padding: 0;
            background: none;
            border: none;
        }

        .content-section>ul>li:last-child {
            margin-bottom: 0;
        }

        .content-section>ul>li strong {
            color: #1976D2;
            font-weight: 700;
        }

        /* 子列表样式 */
        .content-section ul ul {
            margin-top: var(--spacing-sm);
            margin-bottom: 0;
            padding-left: var(--spacing-lg);
            list-style: none;
            background: none;
            border: none;

        }

        .content-section ul ul::before {
            display: none;
        }

        .content-section ul ul li {
            padding: var(--spacing-xs) 0;
            margin-bottom: var(--spacing-xs);
            background: none;
            border: none;
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            position: relative;
            padding-left: var(--spacing-md);
        }

        .content-section ul ul li::before {
            content: '•';
            position: absolute;
            left: 0;
            color: #1976D2;
            font-weight: bold;
        }

        .content-section ul ul li:hover {
            color: var(--text-primary);
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .content-section ul {
                padding: var(--spacing-md);
            }

            .content-section ul::before {
                font-size: var(--font-size-xl);
                top: var(--spacing-md);
                right: var(--spacing-md);
            }
        }
    </style>

</head>

<body>
    <!-- 页面头部 -->
    <header>
        <div class="header-container container">
            <div class="header-content text-center">
                <div class="header-icon">
                    <i class="fas fa-industry"></i>
                </div>
                <h1 class="header-title">二维聚合物材料涂料/工业防腐领域价值链分析报告</h1>
                <h2 class="header-subtitle">数据验证版</h2>
                <div class="header-meta">
                    <div class="header-meta-item flex-items-center">
                        <i class="fas fa-calendar-alt"></i>
                        <span>报告生成: 2024年3月</span>
                    </div>
                    <div class="header-meta-item flex-items-center">
                        <i class="fas fa-database"></i>
                        <span>数据来源: 多方验证</span>
                    </div>
                    <div class="header-meta-item flex-items-center">
                        <i class="fas fa-clock"></i>
                        <span>更新周期: 季度</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 导航栏 -->
    <nav>
        <div class="nav-container container">
            <ul class="nav-list">
                <li><a href="#value-chain-overview" class="nav-link">价值链全景</a></li>
                <li><a href="#executive-summary" class="nav-link">执行摘要</a></li>
                <li><a href="#market-analysis" class="nav-link">市场规模验证</a></li>
                <li><a href="#tech-trends" class="nav-link">技术趋势</a></li>
                <li><a href="#competition" class="nav-link">竞争格局</a></li>
                <li><a href="#value-chain" class="nav-link">价值链分析</a></li>
                <li><a href="#strategy" class="nav-link">战略建议</a></li>
                <li><a href="#risk" class="nav-link">风险评估</a></li>
                <li><a href="#conclusion" class="nav-link">结论</a></li>

            </ul>
        </div>
    </nav>
    <!-- 价值链全景图 -->
    <section id="value-chain-overview" class="mb-12" style="margin: 30px auto;">
        <div class="text-center mb-8">
            <h2 style="margin-bottom: 10px;">二维聚合物材料涂料产业价值链全景图</h2>
            <p style="margin-bottom: 30px;">2024年全球市场规模：1,100亿元 | 2030年预测：1,750亿元 | CAGR: 8.1% |
                中国市场：265亿元(CAGR 12.5%)</p>
        </div>
        <div class="bg-white rounded-xl p-6 card-shadow mb-8">
            <style>
                .value-chain-container * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }

                .value-chain-container {
                    margin: 0 auto;
                    border-radius: 10px;
                    padding: 0 30px;
                }

                .value-chain {
                    display: flex;
                    justify-content: space-between;
                    align-items: stretch;
                    margin-bottom: 10px;
                    gap: 4px;
                }

                .chain-section {
                    flex: 1;
                    border-radius: 8px;
                    padding: 15px;
                    min-height: 500px;
                }

                /* 给中游涂料制造分配更多空间 */
                .midstream-manufacturing {
                    flex: 1.8;
                }

                /* 上游和分销商部分增大 */
                .upstream,
                .midstream:not(.midstream-manufacturing) {
                    flex: 0.9;
                }

                /* 中游涂料制造部分的特殊样式 */
                .midstream-manufacturing .downstream-grid {
                    gap: 15px;
                }

                .midstream-manufacturing .category {
                    padding: 14px;
                }

                /* 下游部分特殊布局 - 适度缩小 */
                .downstream {
                    flex: 1.6;
                    /* 下游部分适度缩小 */
                }

                .downstream-grid {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    /* 固定两列布局 */
                    gap: 12px;
                }

                /* 分销渠道占满底部 */
                .distribution-full-width {
                    grid-column: 1 / -1;
                    /* 跨越所有列 */
                }

                .upstream {
                    background: #E3F2FD;
                    border: 2px solid #2196F3;
                }

                .midstream {
                    background: #E8F5E8;
                    border: 2px solid #4CAF50;
                }

                /* 分销渠道商专用颜色 */
                .distribution {
                    background: #E0F2F1;
                    border: 2px solid #4DB6AC;
                }

                .downstream {
                    background: #FFF3E0;
                    border: 2px solid #FF9800;
                }

                .section-title1 {
                    font-size: 16px;
                    font-weight: bold;
                    text-align: center;
                    margin-bottom: 15px;
                    color: #333;
                }

                .category {
                    margin-bottom: 15px;
                    background: white;
                    border-radius: 6px;
                    padding: 12px;
                    border: 1px solid #ddd;
                    transition: all 0.3s ease;
                }

                .category:hover {
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                    transform: translateY(-2px);
                }

                .category-title {
                    font-size: 13px;
                    font-weight: bold;
                    margin-bottom: 8px;
                    color: #333;
                    position: relative;
                }




                @keyframes pulse {
                    0% {
                        transform: scale(1);
                    }

                    50% {
                        transform: scale(1.05);
                    }

                    100% {
                        transform: scale(1);
                    }
                }

                .companies {
                    font-size: 11px;
                    line-height: 1.4;
                    color: #555;
                }

                .arrow {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 24px;
                    font-weight: bold;
                    color: #444;
                    width: 30px;
                }

                .flow-description {
                    text-align: center;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 10px;
                    border-radius: 6px;
                    margin: 15px 0;
                    font-size: 14px;
                    font-weight: bold;
                    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
                }

                .legend {
                    display: flex;
                    justify-content: center;
                    gap: 20px;
                    margin-top: 15px;
                    flex-wrap: wrap;
                }

                .legend-item {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    font-size: 12px;
                }

                .legend-color {
                    width: 16px;
                    height: 16px;
                    border-radius: 3px;
                    border: 1px solid #ccc;
                }

                .upstream-color {
                    background: #E3F2FD;
                }

                .midstream-color {
                    background: #E8F5E8;
                }

                .distribution-color {
                    background: #E0F2F1;
                }

                .downstream-color {
                    background: #FFF3E0;
                }

                /* 竞争关系颜色定义 */
                .direct-competitor {
                    background: #ffebee;
                    color: #d32f2f;
                    border: 2px solid #ffcdd2;
                }

                .indirect-competitor {
                    background: #fff8e1;
                    color: #f57f17;
                    border: 2px solid #fff3c4;
                }

                .primary-supplier {
                    background: #e3f2fd;
                    color: #1976d2;
                    border: 2px solid #bbdefb;
                }

                .secondary-supplier {
                    background: #f3e5f5;
                    color: #7b1fa2;
                    border: 2px solid #e1bee7;
                }

                /* 图例颜色块 */
                .direct-competitor-color {
                    background: #ffebee;
                }

                .indirect-competitor-color {
                    background: #fff8e1;
                }

                .primary-supplier-color {
                    background: #e3f2fd;
                }

                .secondary-supplier-color {
                    background: #f3e5f5;
                }
            </style>

            <div class="value-chain-container">
                <div class="value-chain">
                    <!-- 上游：原材料供应 -->
                    <div class="chain-section upstream">
                        <div class="section-title1">上游：原材料供应</div>

                        <div class="category direct-competitor">
                            <div class="category-title">石墨烯/二维材料供应商</div>
                            <div class="companies">
                                • 常州第六元素材料科技（5亿元，中国20%）<br>
                                • 江苏先丰纳米材料科技（3亿元，中国15%）<br>
                                • XG Sciences 美国（10.5亿元，全球12%）<br>
                                • Graphenea 西班牙（1.5亿元，欧洲8%）<br>
                                • 北京墨烯控股集团（10亿元，中国12%）<br>
                                • 宁波墨西科技（4亿元，中国8%）<br>
                                • 济南圣泉集团（20亿元，中国10%）<br>
                                • 贝特瑞新材料集团（100亿元，中国15%）<br>
                                • ACS Materials 美国（5.6亿元，全球5%）<br>
                                • Thomas Swan 英国（8.4亿元，欧洲10%）<br>
                            </div>
                        </div>

                        <div class="category indirect-competitor">
                            <div class="category-title">聚合物基材供应商</div>
                            <div class="companies">
                                • BASF 巴斯夫（6,090亿元，18%）<br>
                                • Dow Chemical 陶氏（3,920亿元，15%）<br>
                                • 万华化学集团（1,500亿元，8%）<br>
                                • SABIC 沙特基础工业（2,450亿元，12%）<br>
                                • Covestro 科思创（1,260亿元，9%）<br>
                                • DuPont 杜邦（1,400亿元，10%）<br>
                                • LG Chem LG化学（2,240亿元，10%）<br>
                                • Evonik 赢创（1,050亿元，8%）<br>
                                • 中国石化（21,000亿元，15%）<br>
                                • Mitsubishi Chemical（2,100亿元，8%）<br>
                            </div>
                        </div>

                        <div class="category indirect-competitor">
                            <div class="category-title">特种添加剂供应商</div>
                            <div class="companies">
                                • BYK-Chemie 毕克化学（140亿元，12%）<br>
                                • Lubrizol 路博润（490亿元，15%）<br>
                                • Huntsman 亨斯迈（560亿元，10%）<br>
                                • Elementis（56亿元，5%）<br>
                                • Clariant 科莱恩（280亿元，8%）<br>
                                • 毕克化学中国（15亿元，20%）<br>
                                • Eastman Chemical（700亿元，12%）<br>
                                • Croda International（105亿元，6%）<br>
                                • 海名斯精细化工（8亿元，10%）<br>
                                • 德谦化学（10亿元，12%）<br>
                            </div>
                        </div>
                    </div>

                    <!-- 箭头 -->
                    <div class="arrow">→</div>

                    <!-- 中游：涂料制造 -->
                    <div class="chain-section midstream midstream-manufacturing">
                        <div class="section-title1">中游：涂料制造</div>
                        <div class="downstream-grid">
                            <div class="category primary-supplier">
                                <div class="category-title">全球领先涂料企业</div>
                                <div class="companies">
                                    • Sherwin-Williams（1,365亿元，12%）<br>
                                    • PPG Industries（1,190亿元，10%）<br>
                                    • AkzoNobel（840亿元，8%）<br>
                                    • 立邦涂料（600亿元，6%）<br>
                                    • Hempel（280亿元，4%）<br>
                                    • Axalta（567亿元，5%）<br>
                                    • Jotun（259亿元，3%）<br>
                                    • 关西涂料（280亿元，3%）<br>
                                    • RPM International（490亿元，4%）<br>
                                    • BASF Coatings（420亿元，4%）
                                </div>
                            </div>

                            <div class="category primary-supplier">
                                <div class="category-title">中国本土涂料企业</div>
                                <div class="companies">
                                    • 三棵树涂料（80亿元，7.3%）<br>
                                    • 嘉宝莉化工集团（50亿元，4.5%）<br>
                                    • 湘江涂料集团（60亿元，5.5%）<br>
                                    • 紫荆花涂料集团（40亿元，3.6%）<br>
                                    • 展辰新材料集团（30亿元，2.7%）<br>
                                    • 巴德士化工（35亿元，3.2%）<br>
                                    • 大宝化工（25亿元，2.3%）<br>
                                    • 中涂化工（20亿元，1.8%）<br>
                                    • 富思特新材料科技（15亿元，1.4%）<br>
                                    • 金力泰化工（12亿元，1.1%）<br>
                                </div>
                            </div>

                            <div class="category primary-supplier">
                                <div class="category-title">国际区域涂料企业</div>
                                <div class="companies">
                                    • Asian Paints 印度（245亿元，南亚25%）<br>
                                    • Berger Paints 印度（70亿元，南亚8%）<br>
                                    • TOA Paint 泰国（35亿元，东南亚20%）<br>
                                    • Kansai Nerolac 印度（56亿元，南亚6%）<br>
                                    • Tiger Coatings 奥地利（21亿元，欧洲3%）<br>
                                    • Beckers Group 瑞典（105亿元，欧洲5%）<br>
                                    • Tikkurila 芬兰（42亿元，北欧15%）<br>
                                    • Sika AG 瑞士（700亿元，全球3%）<br>
                                    • Teknos Group 芬兰（28亿元，北欧10%）<br>
                                    • Crown Paints 肯尼亚（7亿元，东非30%）<br>
                                </div>
                            </div>
                        </div>

                    </div>

                    <!-- 箭头 -->
                    <div class="arrow">→</div>

                    <!-- 分销渠道商 -->
                    <div class="chain-section distribution">
                        <div class="section-title1">分销渠道商</div>

                        <div class="category secondary-supplier">
                            <div class="category-title">国际化工分销商</div>
                            <div class="companies">
                                • Brenntag 德国（1,295亿元，全球77国）<br>
                                • Univar Solutions（693亿元，全球65国）<br>
                                • IMCD Group（315亿元，全球50国）<br>
                                • DKSH 瑞士（840亿元，亚太36国）<br>
                                • Azelis 比利时（245亿元，全球45国）<br>
                                • Helm AG 德国（203亿元，全球30国）<br>
                                • Sinochem 中化集团（5,600亿元，全球150国）<br>
                                • Nexeo Solutions（280亿元，北美为主）<br>
                                • Barentz 荷兰（154亿元，全球60国）<br>
                                • Ravago 比利时（980亿元，全球55国）
                            </div>
                        </div>

                        <div class="category secondary-supplier">
                            <div class="category-title">中国区域分销商</div>
                            <div class="companies">
                                • 上海国药化学试剂（100亿元，华东6省）<br>
                                • 江苏汇鸿国际集团（150亿元，华东为主）<br>
                                • 浙江传化集团（500亿元，全国）<br>
                                • 广州化工进出口（80亿元，华南5省）<br>
                                • 厦门建发化工（35亿元，东南沿海）<br>
                                • 北京化工集团（60亿元，华北5省）<br>
                                • 天津渤海化工（40亿元，京津冀）<br>
                                • 深圳海王化工（30亿元，珠三角）<br>
                                • 青岛海湾化学（25亿元，山东）<br>
                                • 大连化工进出口（20亿元，东北三省）
                            </div>
                        </div>
                    </div>

                    <!-- 箭头 -->
                    <div class="arrow">→</div>

                    <!-- 下游：应用市场 -->
                    <div class="chain-section downstream">
                        <div class="section-title1">下游：应用市场</div>

                        <div class="downstream-grid">
                            <div class="category secondary-supplier">
                                <div class="category-title">石油化工行业</div>
                                <div class="companies">
                                    • 中国石油（85亿元，7.7%）<br>
                                    • 中国石化（92亿元，8.4%）<br>
                                    • 中海油（38亿元，3.5%）<br>
                                    • 沙特阿美中国（25亿元，2.3%）<br>
                                    • 壳牌中国（18亿元，1.6%）<br>
                                    • 埃克森美孚中国（15亿元，1.4%）<br>
                                    • BP中国（12亿元，1.1%）<br>
                                    • 道达尔中国（10亿元，0.9%）<br>
                                    • 恒力石化（22亿元，2%）<br>
                                    • 荣盛石化（20亿元，1.8%）
                                </div>
                            </div>

                            <div class="category secondary-supplier">
                                <div class="category-title">电力能源行业</div>
                                <div class="companies">
                                    • 国家电网（65亿元，5.9%）<br>
                                    • 南方电网（42亿元，3.8%）<br>
                                    • 华能集团（28亿元，2.5%）<br>
                                    • 大唐集团（25亿元，2.3%）<br>
                                    • 国电集团（23亿元，2.1%）<br>
                                    • 华电集团（20亿元，1.8%）<br>
                                    • 三峡集团（35亿元，3.2%）<br>
                                    • 中核集团（15亿元，1.4%）<br>
                                    • 中广核集团（12亿元，1.1%）<br>
                                    • 国投电力（8亿元，0.7%）
                                </div>
                            </div>

                            <div class="category secondary-supplier">
                                <div class="category-title">海洋工程行业</div>
                                <div class="companies">
                                    • 中国船舶集团（48亿元，4.4%）<br>
                                    • 中远海运（35亿元，3.2%）<br>
                                    • 招商局集团（22亿元，2%）<br>
                                    • 江南造船厂（15亿元，1.4%）<br>
                                    • 大连造船厂（12亿元，1.1%）<br>
                                    • 外高桥造船（10亿元，0.9%）<br>
                                    • 中集集团（18亿元，1.6%）<br>
                                    • 振华重工（8亿元，0.7%）<br>
                                    • 海油工程（25亿元，2.3%）<br>
                                    • 博迈科海洋工程（5亿元，0.5%）
                                </div>
                            </div>

                            <div class="category secondary-supplier">
                                <div class="category-title">建筑工程行业</div>
                                <div class="companies">
                                    • 中国建筑（55亿元，5%）<br>
                                    • 中国中铁（38亿元，3.5%）<br>
                                    • 中国铁建（35亿元，3.2%）<br>
                                    • 中国交建（42亿元，3.8%）<br>
                                    • 中国中冶（25亿元，2.3%）<br>
                                    • 万科集团（12亿元，1.1%）<br>
                                    • 碧桂园（10亿元，0.9%）<br>
                                    • 保利发展（8亿元，0.7%）<br>
                                    • 绿地集团（15亿元，1.4%）<br>
                                    • 华润置地（6亿元，0.5%）
                                </div>
                            </div>

                            <div class="category secondary-supplier">
                                <div class="category-title">汽车制造行业</div>
                                <div class="companies">
                                    • 比亚迪（18亿元，1.6%）<br>
                                    • 上汽集团（25亿元，2.3%）<br>
                                    • 一汽集团（20亿元，1.8%）<br>
                                    • 东风汽车（15亿元，1.4%）<br>
                                    • 吉利汽车（12亿元，1.1%）<br>
                                    • 长城汽车（10亿元，0.9%）<br>
                                    • 广汽集团（8亿元，0.7%）<br>
                                    • 蔚来汽车（5亿元，0.5%）<br>
                                    • 理想汽车（4亿元，0.4%）<br>
                                    • 小鹏汽车（3亿元，0.3%）
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flow-description">
                    价值链流向：原材料供应商 → 涂料制造商 → 分销渠道商 → 应用市场 → 终端项目
                </div>

                <div class="legend">
                    <div class="legend-item">
                        <div class="legend-color upstream-color"></div>
                        <span>原材料供应商</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color midstream-color"></div>
                        <span>涂料制造商</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color distribution-color"></div>
                        <span>分销渠道商</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color downstream-color"></div>
                        <span>应用市场</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color direct-competitor-color"></div>
                        <span>直接竞争</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color indirect-competitor-color"></div>
                        <span>间接竞争</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color primary-supplier-color"></div>
                        <span>一级供应</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color secondary-supplier-color"></div>
                        <span>二级供应</span>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- 主内容区 -->
    <div class="main-container container">

        <!-- 执行摘要 -->
        <section id="executive-summary">
            <div class="section-header text-center">
                <h2 class="section-title">执行摘要</h2>
                <p class="section-description">基于最新市场数据验证的关键发现和战略洞察</p>
            </div>

            <div class="card">
                <div class="box box-insight">
                    <h3 class="box-title box-title-insight">
                        <i class="fas fa-lightbulb"></i> 核心发现
                    </h3>
                    <p class="box-text">
                        基于最新市场数据验证，全球工业防腐涂料市场在2023年达到3,168亿元人民币，预计2030年将达到4,398亿元，年复合增长率4.7%。中国市场占全球份额35%，2023年防腐涂料市场容量约1,100亿元，预计2025年突破1,500亿元，CAGR为6.7%。二维聚合物材料作为新一代高性能防腐材料，在纳米材料提升防腐寿命30%以上的技术趋势下，具有巨大的市场潜力。
                    </p>
                </div>

                <!-- 关键指标展示 -->
                <div class="grid grid-cols-1 grid-cols-md-2 grid-cols-lg-4">
                    <div class="metric-card-wrapper">
                        <div class="metric-icon-wrapper metric-card-success flex-center">
                            <i class="fas fa-chart-line metric-icon"></i>
                        </div>
                        <div class="metric-value">4.7%</div>
                        <div class="metric-label">全球市场CAGR</div>
                    </div>
                    <div class="metric-card-wrapper">
                        <div class="metric-icon-wrapper metric-card-info flex-center">
                            <i class="fas fa-globe metric-icon"></i>
                        </div>
                        <div class="metric-value">3,168亿元</div>
                        <div class="metric-label">2023年全球市场规模</div>
                    </div>
                    <div class="metric-card-wrapper">
                        <div class="metric-icon-wrapper metric-card-warning flex-center">
                            <i class="fas fa-flag metric-icon"></i>
                        </div>
                        <div class="metric-value">35%</div>
                        <div class="metric-label">中国市场份额</div>
                    </div>
                    <div class="metric-card-wrapper">
                        <div class="metric-icon-wrapper metric-card-danger flex-center">
                            <i class="fas fa-rocket metric-icon"></i>
                        </div>
                        <div class="metric-value">30%+</div>
                        <div class="metric-label">纳米材料性能提升</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 市场规模验证与分析 -->
        <section id="market-analysis">
            <div class="section-header text-center">
                <h2 class="section-title">市场规模验证与分析</h2>
                <p class="section-description">经过多方数据源交叉验证的市场规模数据</p>
            </div>

            <div class="card">
                <h3 class="card-title flex-items-center">
                    <i class="fas fa-globe"></i>
                    全球市场规模
                </h3>
                <div class="data-table custom-scrollbar">
                    <table>
                        <thead>
                            <tr>
                                <th>市场细分</th>
                                <th>2023年规模</th>
                                <th>2030年预测</th>
                                <th>CAGR</th>
                                <th>数据来源</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>全球工业防腐涂料</td>
                                <td>3,168亿元</td>
                                <td>4,398亿元</td>
                                <td>4.7%</td>
                                <td>用户提供数据</td>
                            </tr>
                            <tr>
                                <td>全球防腐工程领域</td>
                                <td>>5,000亿元</td>
                                <td>7,000亿元（估）</td>
                                <td>5.0%</td>
                                <td>用户提供数据</td>
                            </tr>
                            <tr>
                                <td>全球石墨烯涂料</td>
                                <td>11.8亿元</td>
                                <td>58.3亿元</td>
                                <td>23.5%</td>
                                <td>Grand View Research</td>
                            </tr>
                            <tr>
                                <td>全球防腐陶瓷</td>
                                <td>1,440亿元</td>
                                <td>1,880亿元</td>
                                <td>6.8%</td>
                                <td>基于中国占比推算</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="card">
                <h3 class="card-title flex-items-center">
                    <i class="fas fa-flag"></i>
                    中国市场规模
                </h3>
                <div class="data-table custom-scrollbar">
                    <table>
                        <thead>
                            <tr>
                                <th>市场细分</th>
                                <th>2023年规模</th>
                                <th>2025年预测</th>
                                <th>2030年预测</th>
                                <th>CAGR</th>
                                <th>数据来源</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>防腐涂料市场</td>
                                <td>1,100亿元</td>
                                <td>1,500亿元</td>
                                <td>2,100亿元（估）</td>
                                <td>6.7%</td>
                                <td>用户提供数据</td>
                            </tr>
                            <tr>
                                <td>防腐陶瓷市场</td>
                                <td>280亿元（估）</td>
                                <td>360亿元</td>
                                <td>470亿元</td>
                                <td>6.8%</td>
                                <td>用户提供数据</td>
                            </tr>
                            <tr>
                                <td>防腐工程行业</td>
                                <td>1,250亿元</td>
                                <td>1,425亿元（估）</td>
                                <td>1,850亿元（估）</td>
                                <td>6.5%</td>
                                <td>用户提供数据</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="card">
                <h3 class="card-title flex-items-center">
                    <i class="fas fa-pie-chart"></i>
                    市场结构分析（2023年）
                </h3>
                <div class="content-section">
                    <h4>中国防腐工程行业结构：</h4>
                    <ul>
                        <li><strong>工业防腐：52%（650亿元）</strong>
                            <ul>
                                <li>化工行业：35%（227.5亿元）</li>
                                <li>电力行业：25%（162.5亿元）</li>
                                <li>其他工业：40%（260亿元）</li>
                            </ul>
                        </li>
                        <li><strong>建筑防腐：32%（400亿元）</strong></li>
                        <li><strong>海洋防腐：16%（200亿元）</strong></li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- 产品技术趋势验证 -->
        <section id="tech-trends">
            <div class="section-header text-center">
                <h2 class="section-title">产品技术趋势验证</h2>
                <p class="section-description">核心产品市场份额与技术发展方向</p>
            </div>

            <div class="card">
                <h3 class="card-title flex-items-center">
                    <i class="fas fa-flask"></i>
                    核心产品市场份额
                </h3>
                <div class="data-table custom-scrollbar">
                    <table>
                        <thead>
                            <tr>
                                <th>产品类型</th>
                                <th>市场份额</th>
                                <th>特点</th>
                                <th>主要应用</th>
                                <th>增长率</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>环氧树脂涂料</td>
                                <td>35%</td>
                                <td>附着力强，耐酸碱</td>
                                <td>储罐、管道</td>
                                <td>5.2%</td>
                            </tr>
                            <tr>
                                <td>聚氨酯涂料</td>
                                <td>20%</td>
                                <td>耐候性优异</td>
                                <td>桥梁、海上平台</td>
                                <td>6.5%</td>
                            </tr>
                            <tr>
                                <td>富锌底漆</td>
                                <td>15%</td>
                                <td>阴极保护</td>
                                <td>船舶、港口机械</td>
                                <td>4.8%</td>
                            </tr>
                            <tr>
                                <td>氟碳涂料</td>
                                <td>10%</td>
                                <td>超耐候(20年+)</td>
                                <td>沿海建筑</td>
                                <td>7.2%</td>
                            </tr>
                            <tr>
                                <td>纳米复合涂层</td>
                                <td>5%</td>
                                <td>自修复功能</td>
                                <td>高端应用</td>
                                <td><span class="badge badge-success">28%</span></td>
                            </tr>
                            <tr>
                                <td>其他防腐涂料</td>
                                <td>15%</td>
                                <td>多样化</td>
                                <td>各类应用</td>
                                <td>4.5%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="card">
                <h3 class="card-title flex-items-center">
                    <i class="fas fa-rocket"></i>
                    技术发展趋势
                </h3>
                <div class="content-section">
                    <h4>1. 环保化趋势</h4>
                    <ul>
                        <li>2025年中国绿色化率目标：70%</li>
                        <li>水性涂料年增长率：12%</li>
                        <li>低VOC产品市场份额：从2023年的35%增至2025年的50%</li>
                    </ul>

                    <h4>2. 智能化趋势</h4>
                    <ul>
                        <li>自修复涂层CAGR：28%</li>
                        <li>腐蚀监测系统市场规模：2023年15亿元，2030年预计60亿元</li>
                    </ul>

                    <h4>3. 高性能化趋势</h4>
                    <ul>
                        <li>纳米材料应用：提升防腐寿命30%以上</li>
                        <li>石墨烯涂料CAGR：23.5%</li>
                        <li>二维聚合物材料预期性能提升：40-50%</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- 竞争格局验证与更新 -->
        <section id="competition">
            <div class="section-header text-center">
                <h2 class="section-title">竞争格局验证与更新</h2>
                <p class="section-description">全球及中国市场主要参与者分析</p>
            </div>

            <div class="card">
                <h3 class="card-title flex-items-center">
                    <i class="fas fa-trophy"></i>
                    全球市场格局
                </h3>
                <div class="data-table custom-scrollbar">
                    <table>
                        <thead>
                            <tr>
                                <th>排名</th>
                                <th>公司名称</th>
                                <th>2023年收入</th>
                                <th>全球市场份额</th>
                                <th>中国市场份额</th>
                                <th>主要产品</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>Sherwin-Williams</td>
                                <td>1,365亿元</td>
                                <td>12%</td>
                                <td>5%</td>
                                <td>全系列防腐涂料</td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>PPG Industries</td>
                                <td>1,190亿元</td>
                                <td>10%</td>
                                <td>8%</td>
                                <td>工业防腐涂料</td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>AkzoNobel</td>
                                <td>840亿元</td>
                                <td>8%</td>
                                <td>6%</td>
                                <td>海洋防腐涂料</td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td>立邦(Nippon Paint)</td>
                                <td>600亿元</td>
                                <td>6%</td>
                                <td>12%</td>
                                <td>建筑防腐涂料</td>
                            </tr>
                            <tr>
                                <td>5</td>
                                <td>Hempel</td>
                                <td>280亿元</td>
                                <td>4%</td>
                                <td>3%</td>
                                <td>船舶防腐涂料</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="content-section">
                    <p><strong>合计市场份额：</strong></p>
                    <ul>
                        <li>前5家企业合计：全球市场份额40%，中国市场份额34%</li>
                        <li>前10家企业合计：全球市场份额49%，中国市场份额45%</li>
                    </ul>
                </div>
            </div>

            <div class="card">
                <h3 class="card-title flex-items-center">
                    <i class="fas fa-chart-bar"></i>
                    市场集中度变化
                </h3>
                <div class="content-section">
                    <ul>
                        <li>2020年：前十企业市场份额41%</li>
                        <li>2023年：前十企业市场份额49%</li>
                        <li>预计2030年：前十企业市场份额55%</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- 价值链企业分析 -->
        <section id="value-chain">
            <div class="section-header text-center">
                <h2 class="section-title">价值链企业分析（更新版）</h2>
                <p class="section-description">上中下游关键企业详细分析</p>
            </div>

            <!-- 上游原材料供应商 -->
            <div class="card">
                <h3 class="card-title flex-items-center">
                    <i class="fas fa-layer-group"></i>
                    上游原材料供应商（50家）
                </h3>

                <h4 style="margin-top: 20px; margin-bottom: 15px;">石墨烯/二维材料供应商（20家）</h4>
                <div class="data-table custom-scrollbar">
                    <table>
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>公司名称</th>
                                <th>年收入</th>
                                <th>市场份额类型</th>
                                <th>所在地</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>常州第六元素材料科技</td>
                                <td>5亿元</td>
                                <td>中国20%</td>
                                <td>江苏常州</td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>江苏先丰纳米材料科技</td>
                                <td>3亿元</td>
                                <td>中国15%</td>
                                <td>江苏南京</td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>XG Sciences (美国)</td>
                                <td>10.5亿元</td>
                                <td>全球12%</td>
                                <td>美国密歇根</td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td>Graphenea (西班牙)</td>
                                <td>1.5亿元</td>
                                <td>欧洲8%</td>
                                <td>西班牙</td>
                            </tr>
                            <tr>
                                <td>5</td>
                                <td>北京墨烯控股集团</td>
                                <td>10亿元</td>
                                <td>中国12%</td>
                                <td>北京</td>
                            </tr>
                            <tr>
                                <td>6</td>
                                <td>宁波墨西科技</td>
                                <td>4亿元</td>
                                <td>中国8%</td>
                                <td>浙江宁波</td>
                            </tr>
                            <tr>
                                <td>7</td>
                                <td>济南圣泉集团</td>
                                <td>20亿元</td>
                                <td>中国10%</td>
                                <td>山东济南</td>
                            </tr>
                            <tr>
                                <td>8</td>
                                <td>贝特瑞新材料集团</td>
                                <td>100亿元</td>
                                <td>中国15%</td>
                                <td>深圳</td>
                            </tr>
                            <tr>
                                <td>9</td>
                                <td>ACS Materials (美国)</td>
                                <td>5.6亿元</td>
                                <td>全球5%</td>
                                <td>美国加州</td>
                            </tr>
                            <tr>
                                <td>10</td>
                                <td>Thomas Swan (英国)</td>
                                <td>8.4亿元</td>
                                <td>欧洲10%</td>
                                <td>英国</td>
                            </tr>
                            <tr>
                                <td>11</td>
                                <td>德阳烯碳科技</td>
                                <td>2亿元</td>
                                <td>中国5%</td>
                                <td>四川德阳</td>
                            </tr>
                            <tr>
                                <td>12</td>
                                <td>厦门凯纳石墨烯技术</td>
                                <td>2.5亿元</td>
                                <td>中国5%</td>
                                <td>福建厦门</td>
                            </tr>
                            <tr>
                                <td>13</td>
                                <td>重庆墨希科技</td>
                                <td>5亿元</td>
                                <td>中国10%</td>
                                <td>重庆</td>
                            </tr>
                            <tr>
                                <td>14</td>
                                <td>NeoGraf Solutions</td>
                                <td>14亿元</td>
                                <td>北美15%</td>
                                <td>美国俄亥俄</td>
                            </tr>
                            <tr>
                                <td>15</td>
                                <td>Directa Plus (意大利)</td>
                                <td>2.1亿元</td>
                                <td>欧洲5%</td>
                                <td>意大利</td>
                            </tr>
                            <tr>
                                <td>16</td>
                                <td>青岛华高墨烯科技</td>
                                <td>1.5亿元</td>
                                <td>中国3%</td>
                                <td>山东青岛</td>
                            </tr>
                            <tr>
                                <td>17</td>
                                <td>东旭光电石墨烯产业</td>
                                <td>10亿元</td>
                                <td>中国20%</td>
                                <td>北京</td>
                            </tr>
                            <tr>
                                <td>18</td>
                                <td>Applied Graphene Materials</td>
                                <td>3500万元</td>
                                <td>欧洲2%</td>
                                <td>英国</td>
                            </tr>
                            <tr>
                                <td>19</td>
                                <td>深圳烯旺新材料科技</td>
                                <td>8亿元</td>
                                <td>中国15%</td>
                                <td>深圳</td>
                            </tr>
                            <tr>
                                <td>20</td>
                                <td>银基烯碳新材料集团</td>
                                <td>20亿元</td>
                                <td>中国8%</td>
                                <td>江苏</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 中国企业Top10柱状图 -->
                <div style="margin: 40px 0; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                    <h4 id="chartTitle" style="margin-bottom: 20px; text-align: center; color: #333;">
                        中国石墨烯/二维材料供应商TOP10（按年收入）</h4>

                    <!-- 切换按钮 -->
                    <div style="text-align: center; margin-bottom: 20px;">
                        <button id="sortByRevenue" onclick="updateChart('revenue')"
                            style="padding: 8px 16px; margin: 0 5px; background: #3b82f6; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: bold; transition: all 0.3s;">
                            按年收入排序
                        </button>
                        <button id="sortByShare" onclick="updateChart('share')"
                            style="padding: 8px 16px; margin: 0 5px; background: #6b7280; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; transition: all 0.3s;">
                            按市场份额排序
                        </button>
                    </div>

                    <div style="position: relative; height: 400px; margin: 0 auto; max-width: 1200px;">
                        <canvas id="chinaTop10Chart"></canvas>
                    </div>
                </div>

                <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
                <script>
                    // 原始中国企业数据（与表格完全一致）
                    const originalChinaCompanies = [
                        { name: '贝特瑞新材料集团', revenue: 100, share: 15, shareText: '15%', location: '深圳' },
                        { name: '济南圣泉集团', revenue: 20, share: 10, shareText: '10%', location: '山东济南' },
                        { name: '银基烯碳新材料集团', revenue: 20, share: 8, shareText: '8%', location: '江苏' },
                        { name: '东旭光电石墨烯产业', revenue: 10, share: 20, shareText: '20%', location: '北京' },
                        { name: '北京墨烯控股集团', revenue: 10, share: 12, shareText: '12%', location: '北京' },
                        { name: '深圳烯旺新材料科技', revenue: 8, share: 15, shareText: '15%', location: '深圳' },
                        { name: '常州第六元素材料科技', revenue: 5, share: 20, shareText: '20%', location: '江苏常州' },
                        { name: '重庆墨希科技', revenue: 5, share: 10, shareText: '10%', location: '重庆' },
                        { name: '宁波墨西科技', revenue: 4, share: 8, shareText: '8%', location: '浙江宁波' },
                        { name: '江苏先丰纳米材料科技', revenue: 3, share: 15, shareText: '15%', location: '江苏南京' },
                        { name: '德阳烯碳科技', revenue: 2, share: 5, shareText: '5%', location: '四川德阳' },
                        { name: '厦门凯纳石墨烯技术', revenue: 2.5, share: 5, shareText: '5%', location: '福建厦门' },
                        { name: '青岛华高墨烯科技', revenue: 1.5, share: 3, shareText: '3%', location: '山东青岛' }
                    ];

                    let currentChart = null;
                    let currentSortType = 'revenue';

                    function updateChart(sortType) {
                        currentSortType = sortType;

                        // 更新按钮样式
                        const revenueBtn = document.getElementById('sortByRevenue');
                        const shareBtn = document.getElementById('sortByShare');
                        const chartTitle = document.getElementById('chartTitle');

                        if (sortType === 'revenue') {
                            revenueBtn.style.background = '#3b82f6';
                            shareBtn.style.background = '#6b7280';
                            chartTitle.textContent = '中国石墨烯/二维材料供应商TOP10（按年收入）';
                        } else {
                            revenueBtn.style.background = '#6b7280';
                            shareBtn.style.background = '#3b82f6';
                            chartTitle.textContent = '中国石墨烯/二维材料供应商TOP10（按市场份额）';
                        }

                        // 复制并排序数据
                        let sortedCompanies = [...originalChinaCompanies];
                        if (sortType === 'revenue') {
                            sortedCompanies.sort((a, b) => b.revenue - a.revenue);
                        } else {
                            sortedCompanies.sort((a, b) => b.share - a.share);
                        }

                        // 取前10个
                        sortedCompanies = sortedCompanies.slice(0, 10);

                        // 销毁旧图表
                        if (currentChart) {
                            currentChart.destroy();
                        }

                        // 创建新图表
                        const ctx = document.getElementById('chinaTop10Chart').getContext('2d');
                        currentChart = new Chart(ctx, {
                            type: 'bar',
                            data: {
                                labels: sortedCompanies.map(c => c.name),
                                datasets: [{
                                    label: sortType === 'revenue' ? '年收入（亿元）' : '市场份额（%）',
                                    data: sortedCompanies.map(c => sortType === 'revenue' ? c.revenue : c.share),
                                    backgroundColor: [
                                        'rgba(54, 162, 235, 0.8)',
                                        'rgba(75, 192, 192, 0.8)',
                                        'rgba(153, 102, 255, 0.8)',
                                        'rgba(255, 159, 64, 0.8)',
                                        'rgba(255, 99, 132, 0.8)',
                                        'rgba(54, 162, 235, 0.6)',
                                        'rgba(75, 192, 192, 0.6)',
                                        'rgba(153, 102, 255, 0.6)',
                                        'rgba(255, 159, 64, 0.6)',
                                        'rgba(255, 99, 132, 0.6)'
                                    ],
                                    borderColor: [
                                        'rgba(54, 162, 235, 1)',
                                        'rgba(75, 192, 192, 1)',
                                        'rgba(153, 102, 255, 1)',
                                        'rgba(255, 159, 64, 1)',
                                        'rgba(255, 99, 132, 1)',
                                        'rgba(54, 162, 235, 1)',
                                        'rgba(75, 192, 192, 1)',
                                        'rgba(153, 102, 255, 1)',
                                        'rgba(255, 159, 64, 1)',
                                        'rgba(255, 99, 132, 1)'
                                    ],
                                    borderWidth: 2,
                                    borderRadius: 8,
                                    borderSkipped: false,
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: {
                                        display: true,
                                        position: 'top',
                                        labels: {
                                            font: {
                                                size: 14,
                                                weight: 'bold'
                                            },
                                            padding: 20
                                        }
                                    },
                                    tooltip: {
                                        callbacks: {
                                            label: function (context) {
                                                const company = sortedCompanies[context.dataIndex];
                                                if (sortType === 'revenue') {
                                                    return '年收入：' + context.parsed.y + '亿元';
                                                } else {
                                                    return '市场份额：' + context.parsed.y + '%';
                                                }
                                            },
                                            afterLabel: function (context) {
                                                const company = sortedCompanies[context.dataIndex];
                                                if (sortType === 'revenue') {
                                                    return [
                                                        '中国市场份额：' + company.shareText,
                                                        '所在地：' + company.location
                                                    ];
                                                } else {
                                                    return [
                                                        '年收入：' + company.revenue + '亿元',
                                                        '所在地：' + company.location
                                                    ];
                                                }
                                            }
                                        },
                                        backgroundColor: 'rgba(0, 0, 0, 0.9)',
                                        titleFont: {
                                            size: 14,
                                            weight: 'bold'
                                        },
                                        bodyFont: {
                                            size: 13
                                        },
                                        padding: 12,
                                        displayColors: false,
                                        borderColor: 'rgba(255, 255, 255, 0.3)',
                                        borderWidth: 1
                                    }
                                },
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        title: {
                                            display: true,
                                            text: sortType === 'revenue' ? '年收入（亿元）' : '市场份额（%）',
                                            font: {
                                                size: 14,
                                                weight: 'bold'
                                            }
                                        },
                                        grid: {
                                            color: 'rgba(0, 0, 0, 0.05)',
                                            drawBorder: false
                                        },
                                        ticks: {
                                            font: {
                                                size: 12
                                            },
                                            callback: function (value) {
                                                return sortType === 'revenue' ? value + '亿' : value + '%';
                                            }
                                        }
                                    },
                                    x: {
                                        title: {
                                            display: true,
                                            text: '企业名称',
                                            font: {
                                                size: 14,
                                                weight: 'bold'
                                            }
                                        },
                                        grid: {
                                            display: false
                                        },
                                        ticks: {
                                            font: {
                                                size: 11
                                            },
                                            maxRotation: 45,
                                            minRotation: 45
                                        }
                                    }
                                },
                                animation: {
                                    duration: 1000,
                                    easing: 'easeInOutQuart'
                                }
                            }
                        });
                    }

                    // 页面加载时默认显示按年收入排序的图表
                    window.addEventListener('DOMContentLoaded', function () {
                        updateChart('revenue');
                    });
                </script>

                <h4 style="margin-top: 20px; margin-bottom: 15px;">聚合物基材供应商（15家）</h4>
                <div class="data-table custom-scrollbar">
                    <table>
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>公司名称</th>
                                <th>年收入</th>
                                <th>全球份额</th>
                                <th>所在地</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>21</td>
                                <td>BASF (巴斯夫)</td>
                                <td>6,090亿元</td>
                                <td>18%</td>
                                <td>德国</td>
                            </tr>
                            <tr>
                                <td>22</td>
                                <td>Dow Chemical (陶氏)</td>
                                <td>3,920亿元</td>
                                <td>15%</td>
                                <td>美国</td>
                            </tr>
                            <tr>
                                <td>23</td>
                                <td>万华化学集团</td>
                                <td>1,500亿元</td>
                                <td>8%</td>
                                <td>中国山东</td>
                            </tr>
                            <tr>
                                <td>24</td>
                                <td>SABIC (沙特基础工业)</td>
                                <td>2,450亿元</td>
                                <td>12%</td>
                                <td>沙特</td>
                            </tr>
                            <tr>
                                <td>25</td>
                                <td>Covestro (科思创)</td>
                                <td>1,260亿元</td>
                                <td>9%</td>
                                <td>德国</td>
                            </tr>
                            <tr>
                                <td>26</td>
                                <td>DuPont (杜邦)</td>
                                <td>1,400亿元</td>
                                <td>10%</td>
                                <td>美国</td>
                            </tr>
                            <tr>
                                <td>27</td>
                                <td>LG Chem (LG化学)</td>
                                <td>2,240亿元</td>
                                <td>10%</td>
                                <td>韩国</td>
                            </tr>
                            <tr>
                                <td>28</td>
                                <td>Evonik (赢创)</td>
                                <td>1,050亿元</td>
                                <td>8%</td>
                                <td>德国</td>
                            </tr>
                            <tr>
                                <td>29</td>
                                <td>中国石化</td>
                                <td>21,000亿元</td>
                                <td>15%</td>
                                <td>中国</td>
                            </tr>
                            <tr>
                                <td>30</td>
                                <td>Mitsubishi Chemical</td>
                                <td>2,100亿元</td>
                                <td>8%</td>
                                <td>日本</td>
                            </tr>
                            <tr>
                                <td>31</td>
                                <td>Solvay (索尔维)</td>
                                <td>791亿元</td>
                                <td>6%</td>
                                <td>比利时</td>
                            </tr>
                            <tr>
                                <td>32</td>
                                <td>Arkema (阿科玛)</td>
                                <td>665亿元</td>
                                <td>5%</td>
                                <td>法国</td>
                            </tr>
                            <tr>
                                <td>33</td>
                                <td>东丽株式会社</td>
                                <td>1,400亿元</td>
                                <td>7%</td>
                                <td>日本</td>
                            </tr>
                            <tr>
                                <td>34</td>
                                <td>台塑集团</td>
                                <td>5,600亿元</td>
                                <td>8%</td>
                                <td>中国台湾</td>
                            </tr>
                            <tr>
                                <td>35</td>
                                <td>住友化学</td>
                                <td>1,260亿元</td>
                                <td>6%</td>
                                <td>日本</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 聚合物基材供应商Top10柱状图 -->
                <div style="margin: 40px 0; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                    <h4 id="polymerChartTitle" style="margin-bottom: 20px; text-align: center; color: #333;">
                        聚合物基材供应商TOP10（按年收入）</h4>

                    <!-- 切换按钮 -->
                    <div style="text-align: center; margin-bottom: 20px;">
                        <button id="polymerSortByRevenue" onclick="updatePolymerChart('revenue')"
                            style="padding: 8px 16px; margin: 0 5px; background: #3b82f6; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: bold; transition: all 0.3s;">
                            按年收入排序
                        </button>
                        <button id="polymerSortByShare" onclick="updatePolymerChart('share')"
                            style="padding: 8px 16px; margin: 0 5px; background: #6b7280; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; transition: all 0.3s;">
                            按市场份额排序
                        </button>
                    </div>

                    <div style="position: relative; height: 400px; margin: 0 auto; max-width: 1200px;">
                        <canvas id="polymerTop10Chart"></canvas>
                    </div>
                </div>

                <script>
                    // 聚合物基材供应商数据
                    const polymerCompanies = [
                        { name: '中国石化', revenue: 21000, share: 15, shareText: '15%', location: '中国' },
                        { name: 'BASF (巴斯夫)', revenue: 6090, share: 18, shareText: '18%', location: '德国' },
                        { name: '台塑集团', revenue: 5600, share: 8, shareText: '8%', location: '中国台湾' },
                        { name: 'Dow Chemical (陶氏)', revenue: 3920, share: 15, shareText: '15%', location: '美国' },
                        { name: 'SABIC (沙特基础工业)', revenue: 2450, share: 12, shareText: '12%', location: '沙特' },
                        { name: 'LG Chem (LG化学)', revenue: 2240, share: 10, shareText: '10%', location: '韩国' },
                        { name: 'Mitsubishi Chemical', revenue: 2100, share: 8, shareText: '8%', location: '日本' },
                        { name: '万华化学集团', revenue: 1500, share: 8, shareText: '8%', location: '中国山东' },
                        { name: 'DuPont (杜邦)', revenue: 1400, share: 10, shareText: '10%', location: '美国' },
                        { name: '东丽株式会社', revenue: 1400, share: 7, shareText: '7%', location: '日本' },
                        { name: 'Covestro (科思创)', revenue: 1260, share: 9, shareText: '9%', location: '德国' },
                        { name: '住友化学', revenue: 1260, share: 6, shareText: '6%', location: '日本' },
                        { name: 'Evonik (赢创)', revenue: 1050, share: 8, shareText: '8%', location: '德国' },
                        { name: 'Solvay (索尔维)', revenue: 791, share: 6, shareText: '6%', location: '比利时' },
                        { name: 'Arkema (阿科玛)', revenue: 665, share: 5, shareText: '5%', location: '法国' }
                    ];

                    let currentPolymerChart = null;
                    let currentPolymerSortType = 'revenue';

                    function updatePolymerChart(sortType) {
                        currentPolymerSortType = sortType;

                        // 更新按钮样式
                        const revenueBtn = document.getElementById('polymerSortByRevenue');
                        const shareBtn = document.getElementById('polymerSortByShare');
                        const chartTitle = document.getElementById('polymerChartTitle');

                        if (sortType === 'revenue') {
                            revenueBtn.style.background = '#3b82f6';
                            shareBtn.style.background = '#6b7280';
                            chartTitle.textContent = '聚合物基材供应商TOP10（按年收入）';
                        } else {
                            revenueBtn.style.background = '#6b7280';
                            shareBtn.style.background = '#3b82f6';
                            chartTitle.textContent = '聚合物基材供应商TOP10（按全球市场份额）';
                        }

                        // 复制并排序数据
                        let sortedCompanies = [...polymerCompanies];
                        if (sortType === 'revenue') {
                            sortedCompanies.sort((a, b) => b.revenue - a.revenue);
                        } else {
                            sortedCompanies.sort((a, b) => b.share - a.share);
                        }

                        // 取前10个
                        sortedCompanies = sortedCompanies.slice(0, 10);

                        // 销毁旧图表
                        if (currentPolymerChart) {
                            currentPolymerChart.destroy();
                        }

                        // 创建新图表
                        const ctx = document.getElementById('polymerTop10Chart').getContext('2d');
                        currentPolymerChart = new Chart(ctx, {
                            type: 'bar',
                            data: {
                                labels: sortedCompanies.map(c => c.name),
                                datasets: [{
                                    label: sortType === 'revenue' ? '年收入（亿元）' : '全球市场份额（%）',
                                    data: sortedCompanies.map(c => sortType === 'revenue' ? c.revenue : c.share),
                                    backgroundColor: [
                                        'rgba(255, 99, 132, 0.8)',
                                        'rgba(54, 162, 235, 0.8)',
                                        'rgba(255, 206, 86, 0.8)',
                                        'rgba(75, 192, 192, 0.8)',
                                        'rgba(153, 102, 255, 0.8)',
                                        'rgba(255, 159, 64, 0.8)',
                                        'rgba(199, 99, 132, 0.6)',
                                        'rgba(54, 162, 235, 0.6)',
                                        'rgba(255, 206, 86, 0.6)',
                                        'rgba(75, 192, 192, 0.6)'
                                    ],
                                    borderColor: [
                                        'rgba(255, 99, 132, 1)',
                                        'rgba(54, 162, 235, 1)',
                                        'rgba(255, 206, 86, 1)',
                                        'rgba(75, 192, 192, 1)',
                                        'rgba(153, 102, 255, 1)',
                                        'rgba(255, 159, 64, 1)',
                                        'rgba(199, 99, 132, 1)',
                                        'rgba(54, 162, 235, 1)',
                                        'rgba(255, 206, 86, 1)',
                                        'rgba(75, 192, 192, 1)'
                                    ],
                                    borderWidth: 2,
                                    borderRadius: 8,
                                    borderSkipped: false,
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: {
                                        display: true,
                                        position: 'top',
                                        labels: {
                                            font: {
                                                size: 14,
                                                weight: 'bold'
                                            },
                                            padding: 20
                                        }
                                    },
                                    tooltip: {
                                        callbacks: {
                                            label: function (context) {
                                                const company = sortedCompanies[context.dataIndex];
                                                if (sortType === 'revenue') {
                                                    return '年收入：' + context.parsed.y.toLocaleString() + '亿元';
                                                } else {
                                                    return '全球市场份额：' + context.parsed.y + '%';
                                                }
                                            },
                                            afterLabel: function (context) {
                                                const company = sortedCompanies[context.dataIndex];
                                                if (sortType === 'revenue') {
                                                    return [
                                                        '全球市场份额：' + company.shareText,
                                                        '所在地：' + company.location
                                                    ];
                                                } else {
                                                    return [
                                                        '年收入：' + company.revenue.toLocaleString() + '亿元',
                                                        '所在地：' + company.location
                                                    ];
                                                }
                                            }
                                        },
                                        backgroundColor: 'rgba(0, 0, 0, 0.9)',
                                        titleFont: {
                                            size: 14,
                                            weight: 'bold'
                                        },
                                        bodyFont: {
                                            size: 13
                                        },
                                        padding: 12,
                                        displayColors: false,
                                        borderColor: 'rgba(255, 255, 255, 0.3)',
                                        borderWidth: 1
                                    }
                                },
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        title: {
                                            display: true,
                                            text: sortType === 'revenue' ? '年收入（亿元）' : '全球市场份额（%）',
                                            font: {
                                                size: 14,
                                                weight: 'bold'
                                            }
                                        },
                                        grid: {
                                            color: 'rgba(0, 0, 0, 0.05)',
                                            drawBorder: false
                                        },
                                        ticks: {
                                            font: {
                                                size: 12
                                            },
                                            callback: function (value) {
                                                if (sortType === 'revenue') {
                                                    return value >= 1000 ? (value / 1000).toFixed(1) + '千亿' : value + '亿';
                                                } else {
                                                    return value + '%';
                                                }
                                            }
                                        }
                                    },
                                    x: {
                                        title: {
                                            display: true,
                                            text: '企业名称',
                                            font: {
                                                size: 14,
                                                weight: 'bold'
                                            }
                                        },
                                        grid: {
                                            display: false
                                        },
                                        ticks: {
                                            font: {
                                                size: 11
                                            },
                                            maxRotation: 45,
                                            minRotation: 45
                                        }
                                    }
                                },
                                animation: {
                                    duration: 1000,
                                    easing: 'easeInOutQuart'
                                }
                            }
                        });
                    }

                    // 页面加载时默认显示按年收入排序的图表
                    window.addEventListener('DOMContentLoaded', function () {
                        updatePolymerChart('revenue');
                    });
                </script>

                <h4 style="margin-top: 20px; margin-bottom: 15px;">特种添加剂供应商（15家）</h4>
                <div class="data-table custom-scrollbar">
                    <table>
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>公司名称</th>
                                <th>年收入</th>
                                <th>市场份额</th>
                                <th>所在地</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>36</td>
                                <td>BYK-Chemie (毕克化学)</td>
                                <td>140亿元</td>
                                <td>12%</td>
                                <td>德国</td>
                            </tr>
                            <tr>
                                <td>37</td>
                                <td>Lubrizol (路博润)</td>
                                <td>490亿元</td>
                                <td>15%</td>
                                <td>美国</td>
                            </tr>
                            <tr>
                                <td>38</td>
                                <td>Huntsman (亨斯迈)</td>
                                <td>560亿元</td>
                                <td>10%</td>
                                <td>美国</td>
                            </tr>
                            <tr>
                                <td>39</td>
                                <td>Elementis</td>
                                <td>56亿元</td>
                                <td>5%</td>
                                <td>英国</td>
                            </tr>
                            <tr>
                                <td>40</td>
                                <td>Clariant (科莱恩)</td>
                                <td>280亿元</td>
                                <td>8%</td>
                                <td>瑞士</td>
                            </tr>
                            <tr>
                                <td>41</td>
                                <td>毕克化学(中国)</td>
                                <td>15亿元</td>
                                <td>20%</td>
                                <td>中国上海</td>
                            </tr>
                            <tr>
                                <td>42</td>
                                <td>Eastman Chemical</td>
                                <td>700亿元</td>
                                <td>12%</td>
                                <td>美国</td>
                            </tr>
                            <tr>
                                <td>43</td>
                                <td>Croda International</td>
                                <td>105亿元</td>
                                <td>6%</td>
                                <td>英国</td>
                            </tr>
                            <tr>
                                <td>44</td>
                                <td>海名斯精细化工</td>
                                <td>8亿元</td>
                                <td>10%</td>
                                <td>中国江苏</td>
                            </tr>
                            <tr>
                                <td>45</td>
                                <td>德谦化学</td>
                                <td>10亿元</td>
                                <td>12%</td>
                                <td>中国上海</td>
                            </tr>
                            <tr>
                                <td>46</td>
                                <td>Lanxess (朗盛)</td>
                                <td>490亿元</td>
                                <td>8%</td>
                                <td>德国</td>
                            </tr>
                            <tr>
                                <td>47</td>
                                <td>江苏三木化工</td>
                                <td>12亿元</td>
                                <td>15%</td>
                                <td>中国江苏</td>
                            </tr>
                            <tr>
                                <td>48</td>
                                <td>信越化学工业</td>
                                <td>1,050亿元</td>
                                <td>10%</td>
                                <td>日本</td>
                            </tr>
                            <tr>
                                <td>49</td>
                                <td>Troy Corporation</td>
                                <td>35亿元</td>
                                <td>5%</td>
                                <td>美国</td>
                            </tr>
                            <tr>
                                <td>50</td>
                                <td>广州市白云化工</td>
                                <td>5亿元</td>
                                <td>8%</td>
                                <td>中国广东</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 特种添加剂供应商Top10柱状图 -->
                <div style="margin: 40px 0; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                    <h4 id="additiveChartTitle" style="margin-bottom: 20px; text-align: center; color: #333;">
                        特种添加剂供应商TOP10（按年收入）</h4>

                    <!-- 切换按钮 -->
                    <div style="text-align: center; margin-bottom: 20px;">
                        <button id="additiveSortByRevenue" onclick="updateAdditiveChart('revenue')"
                            style="padding: 8px 16px; margin: 0 5px; background: #3b82f6; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: bold; transition: all 0.3s;">
                            按年收入排序
                        </button>
                        <button id="additiveSortByShare" onclick="updateAdditiveChart('share')"
                            style="padding: 8px 16px; margin: 0 5px; background: #6b7280; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; transition: all 0.3s;">
                            按市场份额排序
                        </button>
                    </div>

                    <div style="position: relative; height: 400px; margin: 0 auto; max-width: 1200px;">
                        <canvas id="additiveTop10Chart"></canvas>
                    </div>
                </div>

                <script>
                    // 特种添加剂供应商数据
                    const additiveCompanies = [
                        { name: '信越化学工业', revenue: 1050, share: 10, shareText: '10%', location: '日本' },
                        { name: 'Eastman Chemical', revenue: 700, share: 12, shareText: '12%', location: '美国' },
                        { name: 'Huntsman (亨斯迈)', revenue: 560, share: 10, shareText: '10%', location: '美国' },
                        { name: 'Lubrizol (路博润)', revenue: 490, share: 15, shareText: '15%', location: '美国' },
                        { name: 'Lanxess (朗盛)', revenue: 490, share: 8, shareText: '8%', location: '德国' },
                        { name: 'Clariant (科莱恩)', revenue: 280, share: 8, shareText: '8%', location: '瑞士' },
                        { name: 'BYK-Chemie (毕克化学)', revenue: 140, share: 12, shareText: '12%', location: '德国' },
                        { name: 'Croda International', revenue: 105, share: 6, shareText: '6%', location: '英国' },
                        { name: 'Elementis', revenue: 56, share: 5, shareText: '5%', location: '英国' },
                        { name: 'Troy Corporation', revenue: 35, share: 5, shareText: '5%', location: '美国' },
                        { name: '毕克化学(中国)', revenue: 15, share: 20, shareText: '20%', location: '中国上海' },
                        { name: '江苏三木化工', revenue: 12, share: 15, shareText: '15%', location: '中国江苏' },
                        { name: '德谦化学', revenue: 10, share: 12, shareText: '12%', location: '中国上海' },
                        { name: '海名斯精细化工', revenue: 8, share: 10, shareText: '10%', location: '中国江苏' },
                        { name: '广州市白云化工', revenue: 5, share: 8, shareText: '8%', location: '中国广东' }
                    ];

                    let currentAdditiveChart = null;
                    let currentAdditiveSortType = 'revenue';

                    function updateAdditiveChart(sortType) {
                        currentAdditiveSortType = sortType;

                        // 更新按钮样式
                        const revenueBtn = document.getElementById('additiveSortByRevenue');
                        const shareBtn = document.getElementById('additiveSortByShare');
                        const chartTitle = document.getElementById('additiveChartTitle');

                        if (sortType === 'revenue') {
                            revenueBtn.style.background = '#3b82f6';
                            shareBtn.style.background = '#6b7280';
                            chartTitle.textContent = '特种添加剂供应商TOP10（按年收入）';
                        } else {
                            revenueBtn.style.background = '#6b7280';
                            shareBtn.style.background = '#3b82f6';
                            chartTitle.textContent = '特种添加剂供应商TOP10（按市场份额）';
                        }

                        // 复制并排序数据
                        let sortedCompanies = [...additiveCompanies];
                        if (sortType === 'revenue') {
                            sortedCompanies.sort((a, b) => b.revenue - a.revenue);
                        } else {
                            sortedCompanies.sort((a, b) => b.share - a.share);
                        }

                        // 取前10个
                        sortedCompanies = sortedCompanies.slice(0, 10);

                        // 销毁旧图表
                        if (currentAdditiveChart) {
                            currentAdditiveChart.destroy();
                        }

                        // 创建新图表
                        const ctx = document.getElementById('additiveTop10Chart').getContext('2d');
                        currentAdditiveChart = new Chart(ctx, {
                            type: 'bar',
                            data: {
                                labels: sortedCompanies.map(c => c.name),
                                datasets: [{
                                    label: sortType === 'revenue' ? '年收入（亿元）' : '市场份额（%）',
                                    data: sortedCompanies.map(c => sortType === 'revenue' ? c.revenue : c.share),
                                    backgroundColor: [
                                        'rgba(255, 99, 132, 0.8)',
                                        'rgba(54, 162, 235, 0.8)',
                                        'rgba(255, 206, 86, 0.8)',
                                        'rgba(75, 192, 192, 0.8)',
                                        'rgba(153, 102, 255, 0.8)',
                                        'rgba(255, 159, 64, 0.8)',
                                        'rgba(199, 99, 132, 0.6)',
                                        'rgba(54, 162, 235, 0.6)',
                                        'rgba(255, 206, 86, 0.6)',
                                        'rgba(75, 192, 192, 0.6)'
                                    ],
                                    borderColor: [
                                        'rgba(255, 99, 132, 1)',
                                        'rgba(54, 162, 235, 1)',
                                        'rgba(255, 206, 86, 1)',
                                        'rgba(75, 192, 192, 1)',
                                        'rgba(153, 102, 255, 1)',
                                        'rgba(255, 159, 64, 1)',
                                        'rgba(199, 99, 132, 1)',
                                        'rgba(54, 162, 235, 1)',
                                        'rgba(255, 206, 86, 1)',
                                        'rgba(75, 192, 192, 1)'
                                    ],
                                    borderWidth: 2,
                                    borderRadius: 8,
                                    borderSkipped: false,
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: {
                                        display: true,
                                        position: 'top',
                                        labels: {
                                            font: {
                                                size: 14,
                                                weight: 'bold'
                                            },
                                            padding: 20
                                        }
                                    },
                                    tooltip: {
                                        callbacks: {
                                            label: function (context) {
                                                const company = sortedCompanies[context.dataIndex];
                                                if (sortType === 'revenue') {
                                                    return '年收入：' + context.parsed.y + '亿元';
                                                } else {
                                                    return '市场份额：' + context.parsed.y + '%';
                                                }
                                            },
                                            afterLabel: function (context) {
                                                const company = sortedCompanies[context.dataIndex];
                                                if (sortType === 'revenue') {
                                                    return [
                                                        '市场份额：' + company.shareText,
                                                        '所在地：' + company.location
                                                    ];
                                                } else {
                                                    return [
                                                        '年收入：' + company.revenue + '亿元',
                                                        '所在地：' + company.location
                                                    ];
                                                }
                                            }
                                        },
                                        backgroundColor: 'rgba(0, 0, 0, 0.9)',
                                        titleFont: {
                                            size: 14,
                                            weight: 'bold'
                                        },
                                        bodyFont: {
                                            size: 13
                                        },
                                        padding: 12,
                                        displayColors: false,
                                        borderColor: 'rgba(255, 255, 255, 0.3)',
                                        borderWidth: 1
                                    }
                                },
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        title: {
                                            display: true,
                                            text: sortType === 'revenue' ? '年收入（亿元）' : '市场份额（%）',
                                            font: {
                                                size: 14,
                                                weight: 'bold'
                                            }
                                        },
                                        grid: {
                                            color: 'rgba(0, 0, 0, 0.05)',
                                            drawBorder: false
                                        },
                                        ticks: {
                                            font: {
                                                size: 12
                                            },
                                            callback: function (value) {
                                                return sortType === 'revenue' ? value + '亿' : value + '%';
                                            }
                                        }
                                    },
                                    x: {
                                        title: {
                                            display: true,
                                            text: '企业名称',
                                            font: {
                                                size: 14,
                                                weight: 'bold'
                                            }
                                        },
                                        grid: {
                                            display: false
                                        },
                                        ticks: {
                                            font: {
                                                size: 11
                                            },
                                            maxRotation: 45,
                                            minRotation: 45
                                        }
                                    }
                                },
                                animation: {
                                    duration: 1000,
                                    easing: 'easeInOutQuart'
                                }
                            }
                        });
                    }

                    // 页面加载时默认显示按年收入排序的图表
                    window.addEventListener('DOMContentLoaded', function () {
                        updateAdditiveChart('revenue');
                    });
                </script>
            </div>

            <!-- 中游涂料生产商 -->
            <div class="card">
                <h3 class="card-title flex-items-center">
                    <i class="fas fa-industry"></i>
                    中游涂料生产商（50家）
                </h3>

                <h4 style="margin-top: 20px; margin-bottom: 15px;">全球领先企业</h4>
                <div class="data-table custom-scrollbar">
                    <table>
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>公司名称</th>
                                <th>2023年涂料收入</th>
                                <th>全球份额</th>
                                <th>中国份额</th>
                                <th>主营产品</th>
                                <th>合作潜力</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>Sherwin-Williams</td>
                                <td>1,365亿元</td>
                                <td>12%</td>
                                <td>5%</td>
                                <td>全系列防腐涂料</td>
                                <td>★★★★★</td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>PPG Industries</td>
                                <td>1,190亿元</td>
                                <td>10%</td>
                                <td>8%</td>
                                <td>工业防腐涂料</td>
                                <td>★★★★★</td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>AkzoNobel</td>
                                <td>840亿元</td>
                                <td>8%</td>
                                <td>6%</td>
                                <td>海洋防腐涂料</td>
                                <td>★★★★★</td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td>立邦涂料</td>
                                <td>600亿元</td>
                                <td>6%</td>
                                <td>12%</td>
                                <td>建筑防腐涂料</td>
                                <td>★★★★★</td>
                            </tr>
                            <tr>
                                <td>5</td>
                                <td>Hempel</td>
                                <td>280亿元</td>
                                <td>4%</td>
                                <td>3%</td>
                                <td>船舶防腐涂料</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>6</td>
                                <td>Axalta</td>
                                <td>567亿元</td>
                                <td>5%</td>
                                <td>4%</td>
                                <td>汽车防腐涂料</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>7</td>
                                <td>Jotun</td>
                                <td>259亿元</td>
                                <td>3%</td>
                                <td>2%</td>
                                <td>海洋防腐涂料</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>8</td>
                                <td>关西涂料</td>
                                <td>280亿元</td>
                                <td>3%</td>
                                <td>5%</td>
                                <td>工业防腐涂料</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>9</td>
                                <td>RPM International</td>
                                <td>490亿元</td>
                                <td>4%</td>
                                <td>2%</td>
                                <td>特种防腐涂料</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>10</td>
                                <td>BASF Coatings</td>
                                <td>420亿元</td>
                                <td>4%</td>
                                <td>3%</td>
                                <td>汽车防腐涂料</td>
                                <td>★★★★☆</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 全球领先企业 TOP10 柱状图 -->
                <div style="margin: 40px 0; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                    <h4 id="globalChartTitle" style="margin-bottom: 20px; text-align: center; color: #333;">
                        全球领先企业TOP10（按收入排序）</h4>

                    <!-- 切换按钮 -->
                    <div style="text-align: center; margin-bottom: 20px;">
                        <button id="globalRevenueBtn" onclick="updateGlobalChart('revenue')"
                            style="padding: 8px 16px; margin: 0 5px; background: #3b82f6; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: bold; transition: all 0.3s;">
                            按收入排序
                        </button>
                        <button id="globalGlobalShareBtn" onclick="updateGlobalChart('globalShare')"
                            style="padding: 8px 16px; margin: 0 5px; background: #6b7280; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; transition: all 0.3s;">
                            按全球份额排序
                        </button>
                        <button id="globalChinaShareBtn" onclick="updateGlobalChart('chinaShare')"
                            style="padding: 8px 16px; margin: 0 5px; background: #6b7280; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; transition: all 0.3s;">
                            按中国份额排序
                        </button>
                    </div>

                    <div style="position: relative; height: 400px; margin: 0 auto; max-width: 1200px;">
                        <canvas id="globalChart"></canvas>
                    </div>
                </div>

                <script>
                    // 全球领先企业数据
                    const top10GlobalCoatingCompanies = [
                        { rank: 1, name: 'Sherwin-Williams', revenue: 1365, globalShare: 12, chinaShare: 5, product: '全系列防腐涂料', potential: 5 },
                        { rank: 2, name: 'PPG Industries', revenue: 1190, globalShare: 10, chinaShare: 8, product: '工业防腐涂料', potential: 5 },
                        { rank: 3, name: 'AkzoNobel', revenue: 840, globalShare: 8, chinaShare: 6, product: '海洋防腐涂料', potential: 5 },
                        { rank: 4, name: '立邦涂料', revenue: 600, globalShare: 6, chinaShare: 12, product: '建筑防腐涂料', potential: 5 },
                        { rank: 5, name: 'Axalta', revenue: 567, globalShare: 5, chinaShare: 4, product: '汽车防腐涂料', potential: 4 },
                        { rank: 6, name: 'RPM International', revenue: 490, globalShare: 4, chinaShare: 2, product: '特种防腐涂料', potential: 3 },
                        { rank: 7, name: 'BASF Coatings', revenue: 420, globalShare: 4, chinaShare: 3, product: '汽车防腐涂料', potential: 4 },
                        { rank: 8, name: 'Hempel', revenue: 280, globalShare: 4, chinaShare: 3, product: '船舶防腐涂料', potential: 4 },
                        { rank: 9, name: '关西涂料', revenue: 280, globalShare: 3, chinaShare: 5, product: '工业防腐涂料', potential: 4 },
                        { rank: 10, name: 'Jotun', revenue: 259, globalShare: 3, chinaShare: 2, product: '海洋防腐涂料', potential: 4 }
                    ];

                    let globalChart = null;
                    let currentGlobalSort = 'revenue';

                    // 创建全球企业图表
                    function createGlobalChart(sortType) {
                        const ctx = document.getElementById('globalChart').getContext('2d');

                        // 按指定类型排序并取前10
                        let sortedCompanies = [...top10GlobalCoatingCompanies];
                        if (sortType === 'revenue') {
                            sortedCompanies.sort((a, b) => b.revenue - a.revenue);
                        } else if (sortType === 'globalShare') {
                            sortedCompanies.sort((a, b) => b.globalShare - a.globalShare);
                        } else if (sortType === 'chinaShare') {
                            sortedCompanies.sort((a, b) => b.chinaShare - a.chinaShare);
                        }
                        const top10 = sortedCompanies.slice(0, 10);

                        const labels = top10.map(company => company.name);
                        let data, yAxisLabel, titleText;

                        if (sortType === 'revenue') {
                            data = top10.map(company => company.revenue);
                            yAxisLabel = '收入 (亿元)';
                            titleText = '全球领先企业TOP10 - 按收入排序';
                        } else if (sortType === 'globalShare') {
                            data = top10.map(company => company.globalShare);
                            yAxisLabel = '全球份额 (%)';
                            titleText = '全球领先企业TOP10 - 按全球份额排序';
                        } else if (sortType === 'chinaShare') {
                            data = top10.map(company => company.chinaShare);
                            yAxisLabel = '中国份额 (%)';
                            titleText = '全球领先企业TOP10 - 按中国份额排序';
                        }

                        return new Chart(ctx, {
                            type: 'bar',
                            data: {
                                labels: labels,
                                datasets: [{
                                    label: yAxisLabel,
                                    data: data,
                                    backgroundColor: [
                                        'rgba(255, 99, 132, 0.8)',
                                        'rgba(54, 162, 235, 0.8)',
                                        'rgba(255, 206, 86, 0.8)',
                                        'rgba(75, 192, 192, 0.8)',
                                        'rgba(153, 102, 255, 0.8)',
                                        'rgba(255, 159, 64, 0.8)',
                                        'rgba(199, 99, 132, 0.6)',
                                        'rgba(54, 162, 235, 0.6)',
                                        'rgba(255, 206, 86, 0.6)',
                                        'rgba(75, 192, 192, 0.6)'
                                    ],
                                    borderColor: [
                                        'rgba(255, 99, 132, 1)',
                                        'rgba(54, 162, 235, 1)',
                                        'rgba(255, 206, 86, 1)',
                                        'rgba(75, 192, 192, 1)',
                                        'rgba(153, 102, 255, 1)',
                                        'rgba(255, 159, 64, 1)',
                                        'rgba(199, 99, 132, 1)',
                                        'rgba(54, 162, 235, 1)',
                                        'rgba(255, 206, 86, 1)',
                                        'rgba(75, 192, 192, 1)'
                                    ],
                                    borderWidth: 2,
                                    borderRadius: 8,
                                    borderSkipped: false
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    title: {
                                        display: false
                                    },
                                    legend: {
                                        display: false
                                    },
                                    tooltip: {
                                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                        titleColor: 'white',
                                        bodyColor: 'white',
                                        borderColor: 'rgba(255, 255, 255, 0.2)',
                                        borderWidth: 1,
                                        cornerRadius: 8,
                                        displayColors: false,
                                        callbacks: {
                                            title: function (context) {
                                                const company = top10[context[0].dataIndex];
                                                return `${company.name}`;
                                            },
                                            label: function (context) {
                                                const company = top10[context.dataIndex];
                                                const lines = [
                                                    `收入: ${company.revenue}亿元`,
                                                    `全球份额: ${company.globalShare}%`,
                                                    `中国份额: ${company.chinaShare}%`,
                                                    `主营产品: ${company.product}`,
                                                    `合作潜力: ${getStarRating(company.potential)}`
                                                ];
                                                return lines;
                                            }
                                        }
                                    }
                                },
                                scales: {
                                    x: {
                                        title: {
                                            display: true,
                                            text: '企业名称',
                                            font: {
                                                size: 12,
                                                weight: 'bold'
                                            }
                                        },
                                        ticks: {
                                            maxRotation: 45,
                                            minRotation: 45,
                                            font: {
                                                size: 10
                                            }
                                        }
                                    },
                                    y: {
                                        beginAtZero: true,
                                        title: {
                                            display: true,
                                            text: yAxisLabel,
                                            font: {
                                                size: 12,
                                                weight: 'bold'
                                            }
                                        },
                                        ticks: {
                                            font: {
                                                size: 11
                                            }
                                        }
                                    }
                                },
                                animation: {
                                    duration: 800,
                                    easing: 'easeInOutQuart'
                                }
                            }
                        });
                    }

                    // 更新全球企业图表函数
                    function updateGlobalChart(sortType) {
                        currentGlobalSort = sortType;

                        // 更新按钮样式
                        const revenueBtn = document.getElementById('globalRevenueBtn');
                        const globalShareBtn = document.getElementById('globalGlobalShareBtn');
                        const chinaShareBtn = document.getElementById('globalChinaShareBtn');
                        const chartTitle = document.getElementById('globalChartTitle');

                        // 重置所有按钮样式
                        revenueBtn.style.background = '#6b7280';
                        globalShareBtn.style.background = '#6b7280';
                        chinaShareBtn.style.background = '#6b7280';
                        revenueBtn.style.fontWeight = 'normal';
                        globalShareBtn.style.fontWeight = 'normal';
                        chinaShareBtn.style.fontWeight = 'normal';

                        if (sortType === 'revenue') {
                            revenueBtn.style.background = '#3b82f6';
                            revenueBtn.style.fontWeight = 'bold';
                            chartTitle.textContent = '全球领先企业TOP10（按收入排序）';
                        } else if (sortType === 'globalShare') {
                            globalShareBtn.style.background = '#3b82f6';
                            globalShareBtn.style.fontWeight = 'bold';
                            chartTitle.textContent = '全球领先企业TOP10（按全球份额排序）';
                        } else if (sortType === 'chinaShare') {
                            chinaShareBtn.style.background = '#3b82f6';
                            chinaShareBtn.style.fontWeight = 'bold';
                            chartTitle.textContent = '全球领先企业TOP10（按中国份额排序）';
                        }

                        // 销毁现有图表并创建新图表
                        if (globalChart) {
                            globalChart.destroy();
                        }
                        globalChart = createGlobalChart(sortType);
                    }

                    // 页面加载完成后初始化图表
                    document.addEventListener('DOMContentLoaded', function () {
                        // 确保Chart.js已加载
                        if (typeof Chart !== 'undefined') {
                            updateGlobalChart('revenue');
                        }
                    });
                </script>

                <h4 style="margin-top: 20px; margin-bottom: 15px;">中国本土企业（25家）</h4>
                <div class="data-table custom-scrollbar">
                    <table>
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>公司名称</th>
                                <th>年收入</th>
                                <th>中国市场份额</th>
                                <th>主要产品</th>
                                <th>合作潜力</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>11</td>
                                <td>三棵树涂料</td>
                                <td>80亿元</td>
                                <td>7.3%</td>
                                <td>建筑防腐涂料</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>12</td>
                                <td>嘉宝莉化工集团</td>
                                <td>50亿元</td>
                                <td>4.5%</td>
                                <td>工业防腐涂料</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>13</td>
                                <td>湘江涂料集团</td>
                                <td>60亿元</td>
                                <td>5.5%</td>
                                <td>重防腐涂料</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>14</td>
                                <td>紫荆花涂料集团</td>
                                <td>40亿元</td>
                                <td>3.6%</td>
                                <td>建筑防腐涂料</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>15</td>
                                <td>展辰新材料集团</td>
                                <td>30亿元</td>
                                <td>2.7%</td>
                                <td>工业涂料</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>16</td>
                                <td>巴德士化工</td>
                                <td>35亿元</td>
                                <td>3.2%</td>
                                <td>建筑涂料</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>17</td>
                                <td>大宝化工</td>
                                <td>25亿元</td>
                                <td>2.3%</td>
                                <td>家具涂料</td>
                                <td>★★☆☆☆</td>
                            </tr>
                            <tr>
                                <td>18</td>
                                <td>中涂化工</td>
                                <td>20亿元</td>
                                <td>1.8%</td>
                                <td>防腐涂料</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>19</td>
                                <td>富思特新材料科技</td>
                                <td>15亿元</td>
                                <td>1.4%</td>
                                <td>建筑涂料</td>
                                <td>★★☆☆☆</td>
                            </tr>
                            <tr>
                                <td>20</td>
                                <td>金力泰化工</td>
                                <td>12亿元</td>
                                <td>1.1%</td>
                                <td>汽车涂料</td>
                                <td>★★☆☆☆</td>
                            </tr>
                            <tr>
                                <td>21</td>
                                <td>飞鹿股份</td>
                                <td>8亿元</td>
                                <td>0.7%</td>
                                <td>轨道交通涂料</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>22</td>
                                <td>海虹老人涂料</td>
                                <td>180亿元</td>
                                <td>2%</td>
                                <td>船舶涂料</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>23</td>
                                <td>中远关西涂料</td>
                                <td>45亿元</td>
                                <td>4.1%</td>
                                <td>船舶涂料</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>24</td>
                                <td>信和新材料</td>
                                <td>25亿元</td>
                                <td>2.3%</td>
                                <td>重防腐涂料</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>25</td>
                                <td>北新建材涂料</td>
                                <td>18亿元</td>
                                <td>1.6%</td>
                                <td>建筑涂料</td>
                                <td>★★☆☆☆</td>
                            </tr>
                            <tr>
                                <td>26</td>
                                <td>双虎涂料</td>
                                <td>15亿元</td>
                                <td>1.4%</td>
                                <td>工业涂料</td>
                                <td>★★☆☆☆</td>
                            </tr>
                            <tr>
                                <td>27</td>
                                <td>灯塔涂料</td>
                                <td>12亿元</td>
                                <td>1.1%</td>
                                <td>工业涂料</td>
                                <td>★★☆☆☆</td>
                            </tr>
                            <tr>
                                <td>28</td>
                                <td>鱼童新材料</td>
                                <td>10亿元</td>
                                <td>0.9%</td>
                                <td>防腐涂料</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>29</td>
                                <td>德威涂料</td>
                                <td>8亿元</td>
                                <td>0.7%</td>
                                <td>防腐涂料</td>
                                <td>★★☆☆☆</td>
                            </tr>
                            <tr>
                                <td>30</td>
                                <td>江苏兰陵化工</td>
                                <td>22亿元</td>
                                <td>2%</td>
                                <td>重防腐涂料</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>31</td>
                                <td>西北永新涂料</td>
                                <td>15亿元</td>
                                <td>1.4%</td>
                                <td>防腐涂料</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>32</td>
                                <td>重庆三峡油漆</td>
                                <td>18亿元</td>
                                <td>1.6%</td>
                                <td>工业涂料</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>33</td>
                                <td>科顺防水科技</td>
                                <td>85亿元</td>
                                <td>3%</td>
                                <td>防水防腐材料</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>34</td>
                                <td>东方雨虹</td>
                                <td>350亿元</td>
                                <td>5%</td>
                                <td>防水防腐系统</td>
                                <td>★★★★★</td>
                            </tr>
                            <tr>
                                <td>35</td>
                                <td>亚士创能</td>
                                <td>45亿元</td>
                                <td>2%</td>
                                <td>功能涂料</td>
                                <td>★★★☆☆</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 中国本土企业 TOP10 柱状图 -->
                <div style="margin: 40px 0; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                    <h4 id="chinaChartTitle" style="margin-bottom: 20px; text-align: center; color: #333;">中国本土企业TOP10（按收入排序）</h4>
                    
                    <!-- 切换按钮 -->
                    <div style="text-align: center; margin-bottom: 20px;">
                        <button id="chinaRevenueBtn" onclick="updateChinaChart('revenue')" style="padding: 8px 16px; margin: 0 5px; background: #3b82f6; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: bold; transition: all 0.3s;">
                            按收入排序
                        </button>
                        <button id="chinaChinaShareBtn" onclick="updateChinaChart('chinaShare')" style="padding: 8px 16px; margin: 0 5px; background: #6b7280; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; transition: all 0.3s;">
                            按中国份额排序
                        </button>
                    </div>
                    
                    <div style="position: relative; height: 400px; margin: 0 auto; max-width: 1200px;">
                        <canvas id="chinaChart"></canvas>
                    </div>
                </div>

                <script>
                // 中国本土企业数据
                const top10ChinaCoatingCompanies = [
                    { rank: 1, name: '东方雨虹', revenue: 350, chinaShare: 5.0, product: '防水防腐系统', potential: 5 },
                    { rank: 2, name: '海虹老人涂料', revenue: 180, chinaShare: 2.0, product: '船舶涂料', potential: 4 },
                    { rank: 3, name: '科顺防水科技', revenue: 85, chinaShare: 3.0, product: '防水防腐材料', potential: 4 },
                    { rank: 4, name: '三棵树涂料', revenue: 80, chinaShare: 7.3, product: '建筑防腐涂料', potential: 4 },
                    { rank: 5, name: '湘江涂料集团', revenue: 60, chinaShare: 5.5, product: '重防腐涂料', potential: 4 },
                    { rank: 6, name: '嘉宝莉化工集团', revenue: 50, chinaShare: 4.5, product: '工业防腐涂料', potential: 4 },
                    { rank: 7, name: '中远关西涂料', revenue: 45, chinaShare: 4.1, product: '船舶涂料', potential: 4 },
                    { rank: 8, name: '亚士创能', revenue: 45, chinaShare: 2.0, product: '功能涂料', potential: 3 },
                    { rank: 9, name: '紫荆花涂料集团', revenue: 40, chinaShare: 3.6, product: '建筑防腐涂料', potential: 3 },
                    { rank: 10, name: '巴德士化工', revenue: 35, chinaShare: 3.2, product: '建筑涂料', potential: 3 }
                ];

                let chinaChart = null;
                let currentChinaSort = 'revenue';

                // 创建中国企业图表
                function createChinaChart(sortType) {
                    const ctx = document.getElementById('chinaChart').getContext('2d');
                    
                    // 按指定类型排序并取前10
                    let sortedCompanies = [...top10ChinaCoatingCompanies];
                    if (sortType === 'revenue') {
                        sortedCompanies.sort((a, b) => b.revenue - a.revenue);
                    } else if (sortType === 'chinaShare') {
                        sortedCompanies.sort((a, b) => b.chinaShare - a.chinaShare);
                    }
                    const top10 = sortedCompanies.slice(0, 10);

                    const labels = top10.map(company => company.name);
                    let data, yAxisLabel, titleText;
                    
                    if (sortType === 'revenue') {
                        data = top10.map(company => company.revenue);
                        yAxisLabel = '收入 (亿元)';
                        titleText = '中国本土企业TOP10 - 按收入排序';
                    } else if (sortType === 'chinaShare') {
                        data = top10.map(company => company.chinaShare);
                        yAxisLabel = '中国份额 (%)';
                        titleText = '中国本土企业TOP10 - 按中国份额排序';
                    }

                    return new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: labels,
                            datasets: [{
                                label: yAxisLabel,
                                data: data,
                                backgroundColor: [
                                    'rgba(255, 99, 132, 0.8)',
                                    'rgba(54, 162, 235, 0.8)',
                                    'rgba(255, 206, 86, 0.8)',
                                    'rgba(75, 192, 192, 0.8)',
                                    'rgba(153, 102, 255, 0.8)',
                                    'rgba(255, 159, 64, 0.8)',
                                    'rgba(199, 99, 132, 0.6)',
                                    'rgba(54, 162, 235, 0.6)',
                                    'rgba(255, 206, 86, 0.6)',
                                    'rgba(75, 192, 192, 0.6)'
                                ],
                                borderColor: [
                                    'rgba(255, 99, 132, 1)',
                                    'rgba(54, 162, 235, 1)',
                                    'rgba(255, 206, 86, 1)',
                                    'rgba(75, 192, 192, 1)',
                                    'rgba(153, 102, 255, 1)',
                                    'rgba(255, 159, 64, 1)',
                                    'rgba(199, 99, 132, 1)',
                                    'rgba(54, 162, 235, 1)',
                                    'rgba(255, 206, 86, 1)',
                                    'rgba(75, 192, 192, 1)'
                                ],
                                borderWidth: 2,
                                borderRadius: 8,
                                borderSkipped: false
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                title: {
                                    display: false
                                },
                                legend: {
                                    display: false
                                },
                                tooltip: {
                                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                    titleColor: 'white',
                                    bodyColor: 'white',
                                    borderColor: 'rgba(255, 255, 255, 0.2)',
                                    borderWidth: 1,
                                    cornerRadius: 8,
                                    displayColors: false,
                                    callbacks: {
                                        title: function(context) {
                                            const company = top10[context[0].dataIndex];
                                            return `${company.name}`;
                                        },
                                        label: function(context) {
                                            const company = top10[context.dataIndex];
                                            const lines = [
                                                `收入: ${company.revenue}亿元`,
                                                `中国份额: ${company.chinaShare}%`,
                                                `主要产品: ${company.product}`,
                                                `合作潜力: ${getStarRating(company.potential)}`
                                            ];
                                            return lines;
                                        }
                                    }
                                }
                            },
                            scales: {
                                x: {
                                    title: {
                                        display: true,
                                        text: '企业名称',
                                        font: {
                                            size: 12,
                                            weight: 'bold'
                                        }
                                    },
                                    ticks: {
                                        maxRotation: 45,
                                        minRotation: 45,
                                        font: {
                                            size: 10
                                        }
                                    }
                                },
                                y: {
                                    beginAtZero: true,
                                    title: {
                                        display: true,
                                        text: yAxisLabel,
                                        font: {
                                            size: 12,
                                            weight: 'bold'
                                        }
                                    },
                                    ticks: {
                                        font: {
                                            size: 11
                                        }
                                    }
                                }
                            },
                            animation: {
                                duration: 800,
                                easing: 'easeInOutQuart'
                            }
                        }
                    });
                }

                // 更新中国企业图表函数
                function updateChinaChart(sortType) {
                    currentChinaSort = sortType;
                    
                    // 更新按钮样式
                    const revenueBtn = document.getElementById('chinaRevenueBtn');
                    const chinaShareBtn = document.getElementById('chinaChinaShareBtn');
                    const chartTitle = document.getElementById('chinaChartTitle');
                    
                    if (sortType === 'revenue') {
                        revenueBtn.style.background = '#3b82f6';
                        revenueBtn.style.fontWeight = 'bold';
                        chinaShareBtn.style.background = '#6b7280';
                        chinaShareBtn.style.fontWeight = 'normal';
                        chartTitle.textContent = '中国本土企业TOP10（按收入排序）';
                    } else if (sortType === 'chinaShare') {
                        revenueBtn.style.background = '#6b7280';
                        revenueBtn.style.fontWeight = 'normal';
                        chinaShareBtn.style.background = '#3b82f6';
                        chinaShareBtn.style.fontWeight = 'bold';
                        chartTitle.textContent = '中国本土企业TOP10（按中国份额排序）';
                    }
                    
                    // 销毁现有图表并创建新图表
                    if (chinaChart) {
                        chinaChart.destroy();
                    }
                    chinaChart = createChinaChart(sortType);
                }

                // 页面加载完成后初始化图表
                document.addEventListener('DOMContentLoaded', function() {
                    // 确保Chart.js已加载
                    if (typeof Chart !== 'undefined') {
                        updateChinaChart('revenue');
                    }
                });
                </script>

                <h4 style="margin-top: 20px; margin-bottom: 15px;">国际区域企业（15家）</h4>
                <div class="data-table custom-scrollbar">
                    <table>
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>公司名称</th>
                                <th>年收入</th>
                                <th>区域份额</th>
                                <th>所在地</th>
                                <th>合作潜力</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>36</td>
                                <td>Asian Paints (印度)</td>
                                <td>245亿元</td>
                                <td>南亚25%</td>
                                <td>印度</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>37</td>
                                <td>Berger Paints (印度)</td>
                                <td>70亿元</td>
                                <td>南亚8%</td>
                                <td>印度</td>
                                <td>★★☆☆☆</td>
                            </tr>
                            <tr>
                                <td>38</td>
                                <td>TOA Paint (泰国)</td>
                                <td>35亿元</td>
                                <td>东南亚20%</td>
                                <td>泰国</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>39</td>
                                <td>Kansai Nerolac (印度)</td>
                                <td>56亿元</td>
                                <td>南亚6%</td>
                                <td>印度</td>
                                <td>★★☆☆☆</td>
                            </tr>
                            <tr>
                                <td>40</td>
                                <td>Tiger Coatings (奥地利)</td>
                                <td>21亿元</td>
                                <td>欧洲3%</td>
                                <td>奥地利</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>41</td>
                                <td>Beckers Group (瑞典)</td>
                                <td>105亿元</td>
                                <td>欧洲5%</td>
                                <td>瑞典</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>42</td>
                                <td>Tikkurila (芬兰)</td>
                                <td>42亿元</td>
                                <td>北欧15%</td>
                                <td>芬兰</td>
                                <td>★★☆☆☆</td>
                            </tr>
                            <tr>
                                <td>43</td>
                                <td>Sika AG (瑞士)</td>
                                <td>700亿元</td>
                                <td>全球3%</td>
                                <td>瑞士</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>44</td>
                                <td>Teknos Group (芬兰)</td>
                                <td>28亿元</td>
                                <td>北欧10%</td>
                                <td>芬兰</td>
                                <td>★★☆☆☆</td>
                            </tr>
                            <tr>
                                <td>45</td>
                                <td>Crown Paints (肯尼亚)</td>
                                <td>7亿元</td>
                                <td>东非30%</td>
                                <td>肯尼亚</td>
                                <td>★☆☆☆☆</td>
                            </tr>
                            <tr>
                                <td>46</td>
                                <td>Kansai Paint (南非)</td>
                                <td>14亿元</td>
                                <td>南非20%</td>
                                <td>南非</td>
                                <td>★★☆☆☆</td>
                            </tr>
                            <tr>
                                <td>47</td>
                                <td>Dulux Australia</td>
                                <td>35亿元</td>
                                <td>澳洲25%</td>
                                <td>澳大利亚</td>
                                <td>★★☆☆☆</td>
                            </tr>
                            <tr>
                                <td>48</td>
                                <td>Wattyl (澳大利亚)</td>
                                <td>14亿元</td>
                                <td>澳洲10%</td>
                                <td>澳大利亚</td>
                                <td>★★☆☆☆</td>
                            </tr>
                            <tr>
                                <td>49</td>
                                <td>Boysen (菲律宾)</td>
                                <td>10.5亿元</td>
                                <td>菲律宾40%</td>
                                <td>菲律宾</td>
                                <td>★★☆☆☆</td>
                            </tr>
                            <tr>
                                <td>50</td>
                                <td>National Paints (阿联酋)</td>
                                <td>21亿元</td>
                                <td>中东10%</td>
                                <td>阿联酋</td>
                                <td>★★★☆☆</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 国际区域企业 TOP10 柱状图 -->
                <div style="margin-top: 40px;padding: 20px;background: #f8f9fa;border-radius: 8px;">
                    <div style="position: relative; height: 450px; width: 100%; margin: 20px 0;">
                        <canvas id="internationalChart"></canvas>
                    </div>
                </div>

                <script>
                // 国际区域企业数据
                const top10InternationalCoatingCompanies = [
                    { rank: 1, name: 'Sika AG (瑞士)', revenue: 700, regionShare: '全球3%', location: '瑞士', potential: 4 },
                    { rank: 2, name: 'Asian Paints (印度)', revenue: 245, regionShare: '南亚25%', location: '印度', potential: 3 },
                    { rank: 3, name: 'Beckers Group (瑞典)', revenue: 105, regionShare: '欧洲5%', location: '瑞典', potential: 3 },
                    { rank: 4, name: 'Berger Paints (印度)', revenue: 70, regionShare: '南亚8%', location: '印度', potential: 2 },
                    { rank: 5, name: 'Kansai Nerolac (印度)', revenue: 56, regionShare: '南亚6%', location: '印度', potential: 2 },
                    { rank: 6, name: 'Tikkurila (芬兰)', revenue: 42, regionShare: '北欧15%', location: '芬兰', potential: 2 },
                    { rank: 7, name: 'TOA Paint (泰国)', revenue: 35, regionShare: '东南亚20%', location: '泰国', potential: 3 },
                    { rank: 8, name: 'Dulux Australia', revenue: 35, regionShare: '澳洲25%', location: '澳大利亚', potential: 2 },
                    { rank: 9, name: 'Teknos Group (芬兰)', revenue: 28, regionShare: '北欧10%', location: '芬兰', potential: 2 },
                    { rank: 10, name: 'Tiger Coatings (奥地利)', revenue: 21, regionShare: '欧洲3%', location: '奥地利', potential: 3 }
                ];

                let internationalChart = null;

                // 创建国际企业图表
                function createInternationalChart() {
                    const ctx = document.getElementById('internationalChart').getContext('2d');
                    
                    // 按收入排序
                    let sortedCompanies = [...top10InternationalCoatingCompanies];
                    sortedCompanies.sort((a, b) => b.revenue - a.revenue);
                    const top10 = sortedCompanies.slice(0, 10);

                    const labels = top10.map(company => company.name);
                    const data = top10.map(company => company.revenue);

                    return new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: labels,
                            datasets: [{
                                label: '年收入 (亿元)',
                                data: data,
                                backgroundColor: [
                                    'rgba(255, 99, 132, 0.8)',
                                    'rgba(54, 162, 235, 0.8)',
                                    'rgba(255, 206, 86, 0.8)',
                                    'rgba(75, 192, 192, 0.8)',
                                    'rgba(153, 102, 255, 0.8)',
                                    'rgba(255, 159, 64, 0.8)',
                                    'rgba(199, 99, 132, 0.6)',
                                    'rgba(54, 162, 235, 0.6)',
                                    'rgba(255, 206, 86, 0.6)',
                                    'rgba(75, 192, 192, 0.6)'
                                ],
                                borderColor: [
                                    'rgba(255, 99, 132, 1)',
                                    'rgba(54, 162, 235, 1)',
                                    'rgba(255, 206, 86, 1)',
                                    'rgba(75, 192, 192, 1)',
                                    'rgba(153, 102, 255, 1)',
                                    'rgba(255, 159, 64, 1)',
                                    'rgba(199, 99, 132, 1)',
                                    'rgba(54, 162, 235, 1)',
                                    'rgba(255, 206, 86, 1)',
                                    'rgba(75, 192, 192, 1)'
                                ],
                                borderWidth: 2,
                                borderRadius: 8,
                                borderSkipped: false
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                title: {
                                    display: true,
                                    text: '国际区域企业TOP10 - 按年收入排序',
                                    font: {
                                        size: 16,
                                        weight: 'bold'
                                    },
                                    color: '#2c3e50',
                                    padding: {
                                        top: 10,
                                        bottom: 30
                                    }
                                },
                                legend: {
                                    display: false
                                },
                                tooltip: {
                                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                    titleColor: 'white',
                                    bodyColor: 'white',
                                    borderColor: 'rgba(255, 255, 255, 0.2)',
                                    borderWidth: 1,
                                    cornerRadius: 8,
                                    displayColors: false,
                                    callbacks: {
                                        title: function(context) {
                                            const company = top10[context[0].dataIndex];
                                            return `${company.name}`;
                                        },
                                        label: function(context) {
                                            const company = top10[context.dataIndex];
                                            const lines = [
                                                `年收入: ${company.revenue}亿元`,
                                                `区域份额: ${company.regionShare}`,
                                                `所在地: ${company.location}`,
                                                `合作潜力: ${getStarRating(company.potential)}`
                                            ];
                                            return lines;
                                        }
                                    }
                                }
                            },
                            scales: {
                                x: {
                                    title: {
                                        display: true,
                                        text: '企业名称',
                                        font: {
                                            size: 12,
                                            weight: 'bold'
                                        }
                                    },
                                    ticks: {
                                        maxRotation: 45,
                                        minRotation: 45,
                                        font: {
                                            size: 10
                                        }
                                    }
                                },
                                y: {
                                    beginAtZero: true,
                                    title: {
                                        display: true,
                                        text: '年收入 (亿元)',
                                        font: {
                                            size: 12,
                                            weight: 'bold'
                                        }
                                    },
                                    ticks: {
                                        font: {
                                            size: 11
                                        }
                                    }
                                }
                            },
                            animation: {
                                duration: 800,
                                easing: 'easeInOutQuart'
                            }
                        }
                    });
                }

                // 页面加载完成后初始化图表
                document.addEventListener('DOMContentLoaded', function() {
                    // 确保Chart.js已加载
                    if (typeof Chart !== 'undefined') {
                        internationalChart = createInternationalChart();
                    }
                });
                </script>


                <h2 style="margin-top: 40px; margin-bottom: 15px;">中游涂料生产商综合分析（共 50 家）</h2>
                <!-- 综合收入TOP10柱状图 -->
                <div style="padding: 20px;background: #f8f9fa;border-radius: 8px;">
                    <h4 style="margin-bottom: 20px; text-align: center;  font-size: 18px;">涂料企业综合收入TOP10排行榜（共 50 家）</h4>

                    <div
                        style="position: relative; height: 450px; margin: 0 auto; max-width: 1200px;  padding: 20px; border-radius: 8px;">
                        <canvas id="comprehensiveTop10Chart"></canvas>
                    </div>
                </div>

                <script>
                    // 综合所有涂料企业数据
                    const allCoatingCompanies = [
                        // 全球领先企业
                        { name: 'Sherwin-Williams', revenue: 1365, type: '全球领先', location: '美国', product: '全系列防腐涂料' },
                        { name: 'PPG Industries', revenue: 1190, type: '全球领先', location: '美国', product: '工业防腐涂料' },
                        { name: 'AkzoNobel', revenue: 840, type: '全球领先', location: '荷兰', product: '海洋防腐涂料' },
                        { name: '立邦涂料', revenue: 600, type: '全球领先', location: '日本', product: '建筑防腐涂料' },
                        { name: 'Axalta', revenue: 567, type: '全球领先', location: '美国', product: '汽车防腐涂料' },
                        { name: 'RPM International', revenue: 490, type: '全球领先', location: '美国', product: '特种防腐涂料' },
                        { name: 'BASF Coatings', revenue: 420, type: '全球领先', location: '德国', product: '汽车防腐涂料' },
                        { name: 'Hempel', revenue: 280, type: '全球领先', location: '丹麦', product: '船舶防腐涂料' },
                        { name: '关西涂料', revenue: 280, type: '全球领先', location: '日本', product: '工业防腐涂料' },
                        { name: 'Jotun', revenue: 259, type: '全球领先', location: '挪威', product: '海洋防腐涂料' },

                        // 中国本土企业
                        { name: '东方雨虹', revenue: 350, type: '中国本土', location: '中国', product: '防水防腐系统' },
                        { name: '海虹老人涂料', revenue: 180, type: '中国本土', location: '中国', product: '船舶涂料' },
                        { name: '科顺防水科技', revenue: 85, type: '中国本土', location: '中国', product: '防水防腐材料' },
                        { name: '三棵树涂料', revenue: 80, type: '中国本土', location: '中国', product: '建筑防腐涂料' },
                        { name: '湘江涂料集团', revenue: 60, type: '中国本土', location: '中国', product: '重防腐涂料' },
                        { name: '嘉宝莉化工集团', revenue: 50, type: '中国本土', location: '中国', product: '工业防腐涂料' },
                        { name: '中远关西涂料', revenue: 45, type: '中国本土', location: '中国', product: '船舶涂料' },
                        { name: '亚士创能', revenue: 45, type: '中国本土', location: '中国', product: '功能涂料' },
                        { name: '紫荆花涂料集团', revenue: 40, type: '中国本土', location: '中国', product: '建筑防腐涂料' },
                        { name: '巴德士化工', revenue: 35, type: '中国本土', location: '中国', product: '建筑涂料' },
                        { name: '展辰新材料集团', revenue: 30, type: '中国本土', location: '中国', product: '工业涂料' },
                        { name: '大宝化工', revenue: 25, type: '中国本土', location: '中国', product: '家具涂料' },
                        { name: '信和新材料', revenue: 25, type: '中国本土', location: '中国', product: '重防腐涂料' },
                        { name: '江苏兰陵化工', revenue: 22, type: '中国本土', location: '中国', product: '重防腐涂料' },
                        { name: '中涂化工', revenue: 20, type: '中国本土', location: '中国', product: '防腐涂料' },
                        { name: '北新建材涂料', revenue: 18, type: '中国本土', location: '中国', product: '建筑涂料' },
                        { name: '重庆三峡油漆', revenue: 18, type: '中国本土', location: '中国', product: '工业涂料' },
                        { name: '富思特新材料科技', revenue: 15, type: '中国本土', location: '中国', product: '建筑涂料' },
                        { name: '西北永新涂料', revenue: 15, type: '中国本土', location: '中国', product: '防腐涂料' },
                        { name: '双虎涂料', revenue: 15, type: '中国本土', location: '中国', product: '工业涂料' },
                        { name: '金力泰化工', revenue: 12, type: '中国本土', location: '中国', product: '汽车涂料' },
                        { name: '灯塔涂料', revenue: 12, type: '中国本土', location: '中国', product: '工业涂料' },
                        { name: '鱼童新材料', revenue: 10, type: '中国本土', location: '中国', product: '防腐涂料' },
                        { name: '飞鹿股份', revenue: 8, type: '中国本土', location: '中国', product: '轨道交通涂料' },
                        { name: '德威涂料', revenue: 8, type: '中国本土', location: '中国', product: '防腐涂料' },

                        // 国际区域企业
                        { name: 'Sika AG', revenue: 700, type: '国际区域', location: '瑞士', product: '' },
                        { name: 'Asian Paints', revenue: 245, type: '国际区域', location: '印度', product: '' },
                        { name: 'Beckers Group', revenue: 105, type: '国际区域', location: '瑞典', product: '' },
                        { name: 'Berger Paints', revenue: 70, type: '国际区域', location: '印度', product: '' },
                        { name: 'Kansai Nerolac', revenue: 56, type: '国际区域', location: '印度', product: '' },
                        { name: 'Tikkurila', revenue: 42, type: '国际区域', location: '芬兰', product: '' },
                        { name: 'TOA Paint', revenue: 35, type: '国际区域', location: '泰国', product: '' },
                        { name: 'Dulux Australia', revenue: 35, type: '国际区域', location: '澳大利亚', product: '' },
                        { name: 'Teknos Group', revenue: 28, type: '国际区域', location: '芬兰', product: '' },
                        { name: 'Tiger Coatings', revenue: 21, type: '国际区域', location: '奥地利', product: '' },
                        { name: 'National Paints', revenue: 21, type: '国际区域', location: '阿联酋', product: '' },
                        { name: 'Wattyl', revenue: 14, type: '国际区域', location: '澳大利亚', product: '' },
                        { name: 'Kansai Paint SA', revenue: 14, type: '国际区域', location: '南非', product: '' },
                        { name: 'Boysen', revenue: 10.5, type: '国际区域', location: '菲律宾', product: '' },
                        { name: 'Crown Paints', revenue: 7, type: '国际区域', location: '肯尼亚', product: '' }
                    ];

                    // 排序并取前10
                    const top10Companies = allCoatingCompanies.sort((a, b) => b.revenue - a.revenue).slice(0, 10);

                    // 创建综合图表
                    const comprehensiveCtx = document.getElementById('comprehensiveTop10Chart').getContext('2d');
                    const comprehensiveChart = new Chart(comprehensiveCtx, {
                        type: 'bar',
                        data: {
                            labels: top10Companies.map(c => c.name),
                            datasets: [{
                                label: '年收入（亿元）',
                                data: top10Companies.map(c => c.revenue),
                                backgroundColor: top10Companies.map(c => {
                                    if (c.type === '全球领先') return 'rgba(59, 130, 246, 0.8)';
                                    else if (c.type === '中国本土') return 'rgba(239, 68, 68, 0.8)';
                                    else return 'rgba(34, 197, 94, 0.8)';
                                }),
                                borderColor: top10Companies.map(c => {
                                    if (c.type === '全球领先') return 'rgba(59, 130, 246, 1)';
                                    else if (c.type === '中国本土') return 'rgba(239, 68, 68, 1)';
                                    else return 'rgba(34, 197, 94, 1)';
                                }),
                                borderWidth: 2,
                                borderRadius: 8,
                                borderSkipped: false,
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    display: false
                                },
                                tooltip: {
                                    callbacks: {
                                        title: function (context) {
                                            const company = top10Companies[context[0].dataIndex];
                                            return company.name + ' (' + company.type + ')';
                                        },
                                        label: function (context) {
                                            return '年收入：' + context.parsed.y.toLocaleString() + '亿元';
                                        },
                                        afterLabel: function (context) {
                                            const company = top10Companies[context.dataIndex];
                                            return [
                                                '企业类型：' + company.type,
                                                '所在地：' + company.location,
                                                '主营产品：' + company.product
                                            ];
                                        }
                                    },
                                    backgroundColor: 'rgba(0, 0, 0, 0.9)',
                                    titleFont: {
                                        size: 14,
                                        weight: 'bold'
                                    },
                                    bodyFont: {
                                        size: 13
                                    },
                                    padding: 12,
                                    displayColors: true,
                                    borderColor: 'rgba(255, 255, 255, 0.3)',
                                    borderWidth: 1
                                },
                                // 添加数据标签
                                datalabels: {
                                    display: false
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    title: {
                                        display: true,
                                        text: '年收入（亿元）',
                                        font: {
                                            size: 14,
                                            weight: 'bold'
                                        }
                                    },
                                    grid: {
                                        color: 'rgba(0, 0, 0, 0.05)',
                                        drawBorder: false
                                    },
                                    ticks: {
                                        font: {
                                            size: 12
                                        },
                                        callback: function (value) {
                                            return value >= 1000 ? (value / 1000).toFixed(1) + '千亿' : value + '亿';
                                        }
                                    }
                                },
                                x: {
                                    title: {
                                        display: true,
                                        text: '企业名称',
                                        font: {
                                            size: 14,
                                            weight: 'bold'
                                        }
                                    },
                                    grid: {
                                        display: false
                                    },
                                    ticks: {
                                        font: {
                                            size: 11,
                                            weight: 'bold'
                                        },
                                        maxRotation: 45,
                                        minRotation: 45
                                    }
                                }
                            },
                            animation: {
                                duration: 1500,
                                easing: 'easeInOutQuart'
                            }
                        }
                    });

                    // 添加图例说明
                    const legendHTML = `
                    <div style="text-align: center; margin-top: 5px;">
                        <span style="display: inline-block; margin: 0 15px;">
                            <span style="display: inline-block; width: 20px; height: 12px; background: rgba(59, 130, 246, 0.8); border: 1px solid rgba(59, 130, 246, 1); vertical-align: middle;"></span>
                            <span style="margin-left: 5px; font-size: 13px; color: #333;">全球领先企业</span>
                        </span>
                        <span style="display: inline-block; margin: 0 15px;">
                            <span style="display: inline-block; width: 20px; height: 12px; background: rgba(239, 68, 68, 0.8); border: 1px solid rgba(239, 68, 68, 1); vertical-align: middle;"></span>
                            <span style="margin-left: 5px; font-size: 13px; color: #333;">中国本土企业</span>
                        </span>
                        <span style="display: inline-block; margin: 0 15px;">
                            <span style="display: inline-block; width: 20px; height: 12px; background: rgba(34, 197, 94, 0.8); border: 1px solid rgba(34, 197, 94, 1); vertical-align: middle;"></span>
                            <span style="margin-left: 5px; font-size: 13px; color: #333;">国际区域企业</span>
                        </span>
                    </div>
                `;
                    document.getElementById('comprehensiveTop10Chart').parentElement.insertAdjacentHTML('beforeend', legendHTML);
                </script>

                <!-- 产品类型分布饼图 -->
                <div style="margin: 40px 0;padding: 20px;background: #f8f9fa;border-radius: 8px;">
                    <h4 style="margin-bottom: 20px; text-align: center; color: #333;">涂料产品类型分布（全球领先 & 中国本土企业）</h4>

                    <div style="position: relative; height: 400px; margin: 0 auto; max-width: 600px;">
                        <canvas id="productTypePieChart"></canvas>
                    </div>
                </div>

                <script>
                    // 统计产品类型（仅全球领先和中国本土企业）
                    const productTypeCount = {};
                    const singleCompanyProducts = {}; // 存储只有一家企业的产品类型

                    allCoatingCompanies.forEach(company => {
                        if (company.type === '全球领先' || company.type === '中国本土') {
                            const product = company.product;
                            if (productTypeCount[product]) {
                                productTypeCount[product]++;
                            } else {
                                productTypeCount[product] = 1;
                            }
                        }
                    });

                    // 处理数据：将只有一家的归类为"其他"
                    let otherCount = 0;
                    const otherProducts = [];
                    const processedProductCount = {};

                    Object.entries(productTypeCount).forEach(([product, count]) => {
                        if (count === 1) {
                            otherCount += count;
                            otherProducts.push(product);
                            singleCompanyProducts[product] = true;
                        } else {
                            processedProductCount[product] = count;
                        }
                    });

                    // 如果有"其他"类别，添加到结果中
                    if (otherCount > 0) {
                        processedProductCount['其他'] = otherCount;
                    }

                    // 转换为数组并排序
                    const productTypeData = Object.entries(processedProductCount)
                        .map(([product, count]) => ({ product, count }))
                        .sort((a, b) => {
                            // "其他"类别放在最后
                            if (a.product === '其他') return 1;
                            if (b.product === '其他') return -1;
                            return b.count - a.count;
                        });

                    // 创建饼图
                    const pieCtx = document.getElementById('productTypePieChart').getContext('2d');
                    const pieChart = new Chart(pieCtx, {
                        type: 'pie',
                        data: {
                            labels: productTypeData.map(d => d.product),
                            datasets: [{
                                data: productTypeData.map(d => d.count),
                                backgroundColor: [
                                    'rgba(255, 99, 132, 0.8)',
                                    'rgba(54, 162, 235, 0.8)',
                                    'rgba(255, 206, 86, 0.8)',
                                    'rgba(75, 192, 192, 0.8)',
                                    'rgba(153, 102, 255, 0.8)',
                                    'rgba(255, 159, 64, 0.8)',
                                    'rgba(199, 199, 199, 0.8)',
                                    'rgba(83, 102, 255, 0.8)',
                                    'rgba(255, 99, 255, 0.8)',
                                    'rgba(99, 255, 132, 0.8)',
                                    'rgba(255, 193, 7, 0.8)',
                                    'rgba(0, 188, 212, 0.8)',
                                    'rgba(156, 39, 176, 0.8)',
                                    'rgba(103, 58, 183, 0.8)',
                                    'rgba(63, 81, 181, 0.8)'
                                ],
                                borderColor: [
                                    'rgba(255, 99, 132, 1)',
                                    'rgba(54, 162, 235, 1)',
                                    'rgba(255, 206, 86, 1)',
                                    'rgba(75, 192, 192, 1)',
                                    'rgba(153, 102, 255, 1)',
                                    'rgba(255, 159, 64, 1)',
                                    'rgba(199, 199, 199, 1)',
                                    'rgba(83, 102, 255, 1)',
                                    'rgba(255, 99, 255, 1)',
                                    'rgba(99, 255, 132, 1)',
                                    'rgba(255, 193, 7, 1)',
                                    'rgba(0, 188, 212, 1)',
                                    'rgba(156, 39, 176, 1)',
                                    'rgba(103, 58, 183, 1)',
                                    'rgba(63, 81, 181, 1)'
                                ],
                                borderWidth: 2,
                                borderRadius: 3
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'right',
                                    labels: {
                                        padding: 15,
                                        font: {
                                            size: 12
                                        },
                                        generateLabels: function (chart) {
                                            const data = chart.data;
                                            const total = data.datasets[0].data.reduce((a, b) => a + b, 0);
                                            return data.labels.map((label, i) => {
                                                const value = data.datasets[0].data[i];
                                                const percentage = ((value / total) * 100).toFixed(1);
                                                return {
                                                    text: `${label} (${value}家, ${percentage}%)`,
                                                    fillStyle: data.datasets[0].backgroundColor[i],
                                                    strokeStyle: data.datasets[0].borderColor[i],
                                                    lineWidth: 2,
                                                    hidden: false,
                                                    index: i
                                                };
                                            });
                                        }
                                    }
                                },
                                tooltip: {
                                    callbacks: {
                                        label: function (context) {
                                            const label = context.label || '';
                                            const value = context.parsed;
                                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                            const percentage = ((value / total) * 100).toFixed(1);
                                            return label + ': ' + value + '家 (' + percentage + '%)';
                                        },

                                    },
                                    backgroundColor: 'rgba(0, 0, 0, 0.9)',
                                    titleFont: {
                                        size: 14,
                                        weight: 'bold'
                                    },
                                    bodyFont: {
                                        size: 13
                                    },
                                    padding: 12,
                                    displayColors: true,
                                    borderColor: 'rgba(255, 255, 255, 0.3)',
                                    borderWidth: 1
                                },
                                datalabels: {
                                    display: false
                                }
                            },
                            animation: {
                                animateRotate: true,
                                animateScale: false,
                                duration: 1500
                            }
                        }
                    });
                </script>
            </div>

            <!-- 下游应用企业 -->
            <div class="card">
                <h3 class="card-title flex-items-center">
                    <i class="fas fa-building"></i>
                    下游应用企业（50家）
                </h3>

                <h4 style="margin-top: 20px; margin-bottom: 15px;">石油化工行业</h4>
                <div class="data-table custom-scrollbar">
                    <table>
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>公司名称</th>
                                <th>年防腐采购额</th>
                                <th>占比</th>
                                <th>主要需求</th>
                                <th>合作潜力</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>中国石油</td>
                                <td>85亿元</td>
                                <td>7.7%</td>
                                <td>管道/储罐防腐</td>
                                <td>★★★★★</td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>中国石化</td>
                                <td>92亿元</td>
                                <td>8.4%</td>
                                <td>炼化设备防腐</td>
                                <td>★★★★★</td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>中海油</td>
                                <td>38亿元</td>
                                <td>3.5%</td>
                                <td>海上平台防腐</td>
                                <td>★★★★★</td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td>沙特阿美(中国)</td>
                                <td>25亿元</td>
                                <td>2.3%</td>
                                <td>储运设施防腐</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>5</td>
                                <td>壳牌(中国)</td>
                                <td>18亿元</td>
                                <td>1.6%</td>
                                <td>加油站/储罐</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>6</td>
                                <td>埃克森美孚(中国)</td>
                                <td>15亿元</td>
                                <td>1.4%</td>
                                <td>炼化装置</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>7</td>
                                <td>BP(中国)</td>
                                <td>12亿元</td>
                                <td>1.1%</td>
                                <td>储运设施</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>8</td>
                                <td>道达尔(中国)</td>
                                <td>10亿元</td>
                                <td>0.9%</td>
                                <td>化工装置</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>9</td>
                                <td>恒力石化</td>
                                <td>22亿元</td>
                                <td>2%</td>
                                <td>炼化一体化</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>10</td>
                                <td>荣盛石化</td>
                                <td>20亿元</td>
                                <td>1.8%</td>
                                <td>炼化装置</td>
                                <td>★★★★☆</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h4 style="margin-top: 20px; margin-bottom: 15px;">电力能源行业</h4>
                <div class="data-table custom-scrollbar">
                    <table>
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>公司名称</th>
                                <th>年防腐采购额</th>
                                <th>占比</th>
                                <th>主要需求</th>
                                <th>合作潜力</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>11</td>
                                <td>国家电网</td>
                                <td>65亿元</td>
                                <td>5.9%</td>
                                <td>输电塔防腐</td>
                                <td>★★★★★</td>
                            </tr>
                            <tr>
                                <td>12</td>
                                <td>南方电网</td>
                                <td>42亿元</td>
                                <td>3.8%</td>
                                <td>变电站防腐</td>
                                <td>★★★★★</td>
                            </tr>
                            <tr>
                                <td>13</td>
                                <td>华能集团</td>
                                <td>28亿元</td>
                                <td>2.5%</td>
                                <td>电厂设备防腐</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>14</td>
                                <td>大唐集团</td>
                                <td>25亿元</td>
                                <td>2.3%</td>
                                <td>锅炉/烟囱防腐</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>15</td>
                                <td>国电集团</td>
                                <td>23亿元</td>
                                <td>2.1%</td>
                                <td>冷却塔防腐</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>16</td>
                                <td>华电集团</td>
                                <td>20亿元</td>
                                <td>1.8%</td>
                                <td>脱硫设备防腐</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>17</td>
                                <td>三峡集团</td>
                                <td>35亿元</td>
                                <td>3.2%</td>
                                <td>水电站防腐</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>18</td>
                                <td>中核集团</td>
                                <td>15亿元</td>
                                <td>1.4%</td>
                                <td>核电防腐</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>19</td>
                                <td>中广核集团</td>
                                <td>12亿元</td>
                                <td>1.1%</td>
                                <td>核电防腐</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>20</td>
                                <td>国投电力</td>
                                <td>8亿元</td>
                                <td>0.7%</td>
                                <td>电站防腐</td>
                                <td>★★★☆☆</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h4 style="margin-top: 20px; margin-bottom: 15px;">海洋工程行业</h4>
                <div class="data-table custom-scrollbar">
                    <table>
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>公司名称</th>
                                <th>年防腐采购额</th>
                                <th>占比</th>
                                <th>主要需求</th>
                                <th>合作潜力</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>21</td>
                                <td>中国船舶集团</td>
                                <td>48亿元</td>
                                <td>4.4%</td>
                                <td>船体防腐</td>
                                <td>★★★★★</td>
                            </tr>
                            <tr>
                                <td>22</td>
                                <td>中远海运</td>
                                <td>35亿元</td>
                                <td>3.2%</td>
                                <td>船舶/码头防腐</td>
                                <td>★★★★★</td>
                            </tr>
                            <tr>
                                <td>23</td>
                                <td>招商局集团</td>
                                <td>22亿元</td>
                                <td>2%</td>
                                <td>港口设施防腐</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>24</td>
                                <td>江南造船厂</td>
                                <td>15亿元</td>
                                <td>1.4%</td>
                                <td>造船防腐</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>25</td>
                                <td>大连造船厂</td>
                                <td>12亿元</td>
                                <td>1.1%</td>
                                <td>船舶涂装</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>26</td>
                                <td>外高桥造船</td>
                                <td>10亿元</td>
                                <td>0.9%</td>
                                <td>船体防腐</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>27</td>
                                <td>中集集团</td>
                                <td>18亿元</td>
                                <td>1.6%</td>
                                <td>海工装备防腐</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>28</td>
                                <td>振华重工</td>
                                <td>8亿元</td>
                                <td>0.7%</td>
                                <td>港机防腐</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>29</td>
                                <td>海油工程</td>
                                <td>25亿元</td>
                                <td>2.3%</td>
                                <td>海上平台防腐</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>30</td>
                                <td>博迈科海洋工程</td>
                                <td>5亿元</td>
                                <td>0.5%</td>
                                <td>海工模块防腐</td>
                                <td>★★★☆☆</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h4 style="margin-top: 20px; margin-bottom: 15px;">建筑工程行业</h4>
                <div class="data-table custom-scrollbar">
                    <table>
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>公司名称</th>
                                <th>年防腐采购额</th>
                                <th>占比</th>
                                <th>主要需求</th>
                                <th>合作潜力</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>31</td>
                                <td>中国建筑</td>
                                <td>55亿元</td>
                                <td>5%</td>
                                <td>钢结构防腐</td>
                                <td>★★★★★</td>
                            </tr>
                            <tr>
                                <td>32</td>
                                <td>中国中铁</td>
                                <td>38亿元</td>
                                <td>3.5%</td>
                                <td>桥梁防腐</td>
                                <td>★★★★★</td>
                            </tr>
                            <tr>
                                <td>33</td>
                                <td>中国铁建</td>
                                <td>35亿元</td>
                                <td>3.2%</td>
                                <td>隧道防腐</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>34</td>
                                <td>中国交建</td>
                                <td>42亿元</td>
                                <td>3.8%</td>
                                <td>桥梁/码头防腐</td>
                                <td>★★★★★</td>
                            </tr>
                            <tr>
                                <td>35</td>
                                <td>中国中冶</td>
                                <td>25亿元</td>
                                <td>2.3%</td>
                                <td>钢结构防腐</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>36</td>
                                <td>万科集团</td>
                                <td>12亿元</td>
                                <td>1.1%</td>
                                <td>建筑防腐</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>37</td>
                                <td>碧桂园</td>
                                <td>10亿元</td>
                                <td>0.9%</td>
                                <td>建筑防腐</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>38</td>
                                <td>保利发展</td>
                                <td>8亿元</td>
                                <td>0.7%</td>
                                <td>建筑防腐</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>39</td>
                                <td>绿地集团</td>
                                <td>15亿元</td>
                                <td>1.4%</td>
                                <td>高层建筑防腐</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>40</td>
                                <td>华润置地</td>
                                <td>6亿元</td>
                                <td>0.5%</td>
                                <td>商业建筑防腐</td>
                                <td>★★☆☆☆</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h4 style="margin-top: 20px; margin-bottom: 15px;">汽车制造行业</h4>
                <div class="data-table custom-scrollbar">
                    <table>
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>公司名称</th>
                                <th>年防腐采购额</th>
                                <th>占比</th>
                                <th>主要需求</th>
                                <th>合作潜力</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>41</td>
                                <td>比亚迪</td>
                                <td>18亿元</td>
                                <td>1.6%</td>
                                <td>新能源车防腐</td>
                                <td>★★★★★</td>
                            </tr>
                            <tr>
                                <td>42</td>
                                <td>上汽集团</td>
                                <td>25亿元</td>
                                <td>2.3%</td>
                                <td>汽车涂装</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>43</td>
                                <td>一汽集团</td>
                                <td>20亿元</td>
                                <td>1.8%</td>
                                <td>底盘防腐</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>44</td>
                                <td>东风汽车</td>
                                <td>15亿元</td>
                                <td>1.4%</td>
                                <td>车身防腐</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>45</td>
                                <td>吉利汽车</td>
                                <td>12亿元</td>
                                <td>1.1%</td>
                                <td>整车防腐</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>46</td>
                                <td>长城汽车</td>
                                <td>10亿元</td>
                                <td>0.9%</td>
                                <td>SUV防腐</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>47</td>
                                <td>广汽集团</td>
                                <td>8亿元</td>
                                <td>0.7%</td>
                                <td>乘用车防腐</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>48</td>
                                <td>蔚来汽车</td>
                                <td>5亿元</td>
                                <td>0.5%</td>
                                <td>电动车防腐</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>49</td>
                                <td>理想汽车</td>
                                <td>4亿元</td>
                                <td>0.4%</td>
                                <td>新能源车防腐</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>50</td>
                                <td>小鹏汽车</td>
                                <td>3亿元</td>
                                <td>0.3%</td>
                                <td>电动车防腐</td>
                                <td>★★★☆☆</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 下游应用企业 TOP10 柱状图 -->
                <div style="margin: 40px 0; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                    <h4 id="downstreamChartTitle" style="margin-bottom: 20px; text-align: center; color: #333;">
                        下游应用企业TOP10（按采购金额排序）</h4>

                    <!-- 切换按钮 -->
                    <div style="text-align: center; margin-bottom: 20px;">
                        <button id="downstreamPurchaseBtn" onclick="updateDownstreamChart('purchase')"
                            style="padding: 8px 16px; margin: 0 5px; background: #3b82f6; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: bold; transition: all 0.3s;">
                            按采购金额排序
                        </button>
                        <button id="downstreamShareBtn" onclick="updateDownstreamChart('share')"
                            style="padding: 8px 16px; margin: 0 5px; background: #6b7280; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; transition: all 0.3s;">
                            按市场份额排序
                        </button>
                    </div>

                    <div style="position: relative; height: 400px; margin: 0 auto; max-width: 1200px;">
                        <canvas id="downstreamChart"></canvas>
                    </div>

                    <!-- 图例 -->
                    <div
                        style="display: flex; justify-content: center; align-items: center; gap: 30px; margin-top: 15px; flex-wrap: wrap;">
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <div
                                style="width: 16px; height: 16px; background: rgba(255, 99, 132, 0.8); border-radius: 2px;">
                            </div>
                            <span style="color: #666; font-size: 14px;">石油化工</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <div
                                style="width: 16px; height: 16px; background: rgba(54, 162, 235, 0.8); border-radius: 2px;">
                            </div>
                            <span style="color: #666; font-size: 14px;">电力能源</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <div
                                style="width: 16px; height: 16px; background: rgba(255, 206, 86, 0.8); border-radius: 2px;">
                            </div>
                            <span style="color: #666; font-size: 14px;">海洋工程</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <div
                                style="width: 16px; height: 16px; background: rgba(75, 192, 192, 0.8); border-radius: 2px;">
                            </div>
                            <span style="color: #666; font-size: 14px;">建筑工程</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <div
                                style="width: 16px; height: 16px; background: rgba(153, 102, 255, 0.8); border-radius: 2px;">
                            </div>
                            <span style="color: #666; font-size: 14px;">汽车制造</span>
                        </div>
                    </div>
                </div>

                <script>
                    // 下游应用企业数据
                    const downstreamCompanies = [
                        { name: "中国石油", purchase: 85, share: 7.7, industry: "石油化工", demand: "管道/储罐防腐", cooperation: "★★★★★" },
                        { name: "中国石化", purchase: 92, share: 8.4, industry: "石油化工", demand: "炼化设备防腐", cooperation: "★★★★★" },
                        { name: "中海油", purchase: 38, share: 3.5, industry: "石油化工", demand: "海上平台防腐", cooperation: "★★★★★" },
                        { name: "沙特阿美(中国)", purchase: 25, share: 2.3, industry: "石油化工", demand: "储运设施防腐", cooperation: "★★★★☆" },
                        { name: "壳牌(中国)", purchase: 18, share: 1.6, industry: "石油化工", demand: "加油站/储罐", cooperation: "★★★★☆" },
                        { name: "埃克森美孚(中国)", purchase: 15, share: 1.4, industry: "石油化工", demand: "炼化装置", cooperation: "★★★☆☆" },
                        { name: "BP(中国)", purchase: 12, share: 1.1, industry: "石油化工", demand: "储运设施", cooperation: "★★★☆☆" },
                        { name: "道达尔(中国)", purchase: 10, share: 0.9, industry: "石油化工", demand: "化工装置", cooperation: "★★★☆☆" },
                        { name: "恒力石化", purchase: 22, share: 2.0, industry: "石油化工", demand: "炼化一体化", cooperation: "★★★★☆" },
                        { name: "荣盛石化", purchase: 20, share: 1.8, industry: "石油化工", demand: "炼化装置", cooperation: "★★★★☆" },

                        { name: "国家电网", purchase: 65, share: 5.9, industry: "电力能源", demand: "输电塔防腐", cooperation: "★★★★★" },
                        { name: "南方电网", purchase: 42, share: 3.8, industry: "电力能源", demand: "变电站防腐", cooperation: "★★★★★" },
                        { name: "华能集团", purchase: 28, share: 2.5, industry: "电力能源", demand: "电厂设备防腐", cooperation: "★★★★☆" },
                        { name: "大唐集团", purchase: 25, share: 2.3, industry: "电力能源", demand: "锅炉/烟囱防腐", cooperation: "★★★★☆" },
                        { name: "国电集团", purchase: 23, share: 2.1, industry: "电力能源", demand: "冷却塔防腐", cooperation: "★★★★☆" },
                        { name: "华电集团", purchase: 20, share: 1.8, industry: "电力能源", demand: "脱硫设备防腐", cooperation: "★★★☆☆" },
                        { name: "三峡集团", purchase: 35, share: 3.2, industry: "电力能源", demand: "水电站防腐", cooperation: "★★★★☆" },
                        { name: "中核集团", purchase: 15, share: 1.4, industry: "电力能源", demand: "核电防腐", cooperation: "★★★★☆" },
                        { name: "中广核集团", purchase: 12, share: 1.1, industry: "电力能源", demand: "核电防腐", cooperation: "★★★★☆" },
                        { name: "国投电力", purchase: 8, share: 0.7, industry: "电力能源", demand: "火电防腐", cooperation: "★★★☆☆" },

                        { name: "中海油海工", purchase: 45, share: 4.1, industry: "海洋工程", demand: "海工装备防腐", cooperation: "★★★★★" },
                        { name: "招商局重工", purchase: 32, share: 2.9, industry: "海洋工程", demand: "船舶制造", cooperation: "★★★★☆" },
                        { name: "中远海运重工", purchase: 28, share: 2.5, industry: "海洋工程", demand: "船舶修理", cooperation: "★★★★☆" },
                        { name: "中船重工", purchase: 25, share: 2.3, industry: "海洋工程", demand: "军工船舶", cooperation: "★★★★☆" },
                        { name: "外高桥造船", purchase: 18, share: 1.6, industry: "海洋工程", demand: "大型货轮", cooperation: "★★★☆☆" },
                        { name: "大连船舶重工", purchase: 15, share: 1.4, industry: "海洋工程", demand: "油轮建造", cooperation: "★★★☆☆" },
                        { name: "沪东中华", purchase: 12, share: 1.1, industry: "海洋工程", demand: "LNG船", cooperation: "★★★☆☆" },
                        { name: "江南造船", purchase: 10, share: 0.9, industry: "海洋工程", demand: "特种船舶", cooperation: "★★★☆☆" },
                        { name: "北船重工", purchase: 8, share: 0.7, industry: "海洋工程", demand: "海工平台", cooperation: "★★★☆☆" },
                        { name: "渤船重工", purchase: 6, share: 0.5, industry: "海洋工程", demand: "散货船", cooperation: "★★☆☆☆" },

                        { name: "中建集团", purchase: 55, share: 5.0, industry: "建筑工程", demand: "建筑防腐", cooperation: "★★★★★" },
                        { name: "中铁集团", purchase: 48, share: 4.4, industry: "建筑工程", demand: "桥梁防腐", cooperation: "★★★★★" },
                        { name: "中交集团", purchase: 42, share: 3.8, industry: "建筑工程", demand: "港口防腐", cooperation: "★★★★★" },
                        { name: "中冶集团", purchase: 35, share: 3.2, industry: "建筑工程", demand: "钢结构防腐", cooperation: "★★★★☆" },
                        { name: "中核建设", purchase: 28, share: 2.5, industry: "建筑工程", demand: "核电建设", cooperation: "★★★★☆" },
                        { name: "中电建", purchase: 22, share: 2.0, industry: "建筑工程", demand: "电力建设", cooperation: "★★★★☆" },
                        { name: "万科集团", purchase: 18, share: 1.6, industry: "建筑工程", demand: "住宅防腐", cooperation: "★★★☆☆" },
                        { name: "绿地控股", purchase: 12, share: 1.1, industry: "建筑工程", demand: "商业地产", cooperation: "★★★☆☆" },
                        { name: "保利发展", purchase: 10, share: 0.9, industry: "建筑工程", demand: "地产防腐", cooperation: "★★★☆☆" },
                        { name: "华润置地", purchase: 6, share: 0.5, industry: "建筑工程", demand: "商业建筑防腐", cooperation: "★★☆☆☆" },

                        { name: "比亚迪", purchase: 18, share: 1.6, industry: "汽车制造", demand: "新能源车防腐", cooperation: "★★★★★" },
                        { name: "上汽集团", purchase: 25, share: 2.3, industry: "汽车制造", demand: "汽车涂装", cooperation: "★★★★☆" },
                        { name: "一汽集团", purchase: 20, share: 1.8, industry: "汽车制造", demand: "底盘防腐", cooperation: "★★★★☆" },
                        { name: "东风汽车", purchase: 15, share: 1.4, industry: "汽车制造", demand: "车身防腐", cooperation: "★★★☆☆" },
                        { name: "吉利汽车", purchase: 12, share: 1.1, industry: "汽车制造", demand: "整车防腐", cooperation: "★★★★☆" },
                        { name: "长城汽车", purchase: 10, share: 0.9, industry: "汽车制造", demand: "SUV防腐", cooperation: "★★★☆☆" },
                        { name: "广汽集团", purchase: 8, share: 0.7, industry: "汽车制造", demand: "乘用车防腐", cooperation: "★★★☆☆" },
                        { name: "蔚来汽车", purchase: 5, share: 0.5, industry: "汽车制造", demand: "电动车防腐", cooperation: "★★★★☆" },
                        { name: "理想汽车", purchase: 4, share: 0.4, industry: "汽车制造", demand: "新能源车防腐", cooperation: "★★★☆☆" },
                        { name: "小鹏汽车", purchase: 3, share: 0.3, industry: "汽车制造", demand: "电动车防腐", cooperation: "★★★☆☆" }
                    ];

                    // 行业颜色配置
                    const industryColors = {
                        "石油化工": "rgba(255, 99, 132, 0.8)",
                        "电力能源": "rgba(54, 162, 235, 0.8)",
                        "海洋工程": "rgba(255, 206, 86, 0.8)",
                        "建筑工程": "rgba(75, 192, 192, 0.8)",
                        "汽车制造": "rgba(153, 102, 255, 0.8)"
                    };

                    let downstreamChart = null;
                    let currentDownstreamSort = 'purchase';

                    // 创建下游企业图表
                    function createDownstreamChart(sortType) {
                        const ctx = document.getElementById('downstreamChart').getContext('2d');

                        // 按指定类型排序并取前10
                        let sortedCompanies = [...downstreamCompanies];
                        if (sortType === 'purchase') {
                            sortedCompanies.sort((a, b) => b.purchase - a.purchase);
                        } else {
                            sortedCompanies.sort((a, b) => b.share - a.share);
                        }
                        const top10 = sortedCompanies.slice(0, 10);

                        const labels = top10.map(company => company.name);
                        const data = top10.map(company => sortType === 'purchase' ? company.purchase : company.share);
                        const colors = top10.map(company => industryColors[company.industry]);

                        return new Chart(ctx, {
                            type: 'bar',
                            data: {
                                labels: labels,
                                datasets: [{
                                    label: sortType === 'purchase' ? '采购金额 (亿元)' : '市场份额 (%)',
                                    data: data,
                                    backgroundColor: colors,
                                    borderColor: colors.map(color => color.replace('0.8', '1')),
                                    borderWidth: 2,
                                    borderRadius: 8,
                                    borderSkipped: false,
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: {
                                        display: true,
                                        position: 'top',
                                        labels: {
                                            font: {
                                                size: 14,
                                                weight: 'bold'
                                            },
                                            padding: 20
                                        }
                                    },
                                    tooltip: {
                                        callbacks: {
                                            label: function (context) {
                                                const company = top10[context.dataIndex];
                                                if (sortType === 'purchase') {
                                                    return '采购金额：' + context.parsed.y + '亿元';
                                                } else {
                                                    return '市场份额：' + context.parsed.y + '%';
                                                }
                                            },
                                            afterLabel: function (context) {
                                                const company = top10[context.dataIndex];
                                                if (sortType === 'purchase') {
                                                    return [
                                                        '市场份额：' + company.share + '%',
                                                        '主要需求：' + company.demand,
                                                        '合作潜力：' + company.cooperation
                                                    ];
                                                } else {
                                                    return [
                                                        '采购金额：' + company.purchase + '亿元',
                                                        '主要需求：' + company.demand,
                                                        '合作潜力：' + company.cooperation
                                                    ];
                                                }
                                            }
                                        },
                                        backgroundColor: 'rgba(0, 0, 0, 0.9)',
                                        titleFont: {
                                            size: 14,
                                            weight: 'bold'
                                        },
                                        bodyFont: {
                                            size: 13
                                        },
                                        padding: 12,
                                        displayColors: false,
                                        borderColor: 'rgba(255, 255, 255, 0.3)',
                                        borderWidth: 1
                                    }
                                },
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        title: {
                                            display: true,
                                            text: sortType === 'purchase' ? '采购金额 (亿元)' : '市场份额 (%)',
                                            font: {
                                                size: 14,
                                                weight: 'bold'
                                            }
                                        },
                                        grid: {
                                            color: 'rgba(0, 0, 0, 0.05)',
                                            drawBorder: false
                                        },
                                        ticks: {
                                            font: {
                                                size: 12
                                            }
                                        }
                                    },
                                    x: {
                                        title: {
                                            display: true,
                                            text: '企业名称',
                                            font: {
                                                size: 14,
                                                weight: 'bold'
                                            }
                                        },
                                        grid: {
                                            display: false
                                        },
                                        ticks: {
                                            font: {
                                                size: 11
                                            },
                                            maxRotation: 45,
                                            minRotation: 45
                                        }
                                    }
                                },
                                animation: {
                                    duration: 1000,
                                    easing: 'easeInOutQuart'
                                }
                            }
                        });
                    }

                    // 更新图表函数
                    function updateDownstreamChart(sortType) {
                        currentDownstreamSort = sortType;

                        // 更新按钮样式
                        const purchaseBtn = document.getElementById('downstreamPurchaseBtn');
                        const shareBtn = document.getElementById('downstreamShareBtn');
                        const chartTitle = document.getElementById('downstreamChartTitle');

                        if (sortType === 'purchase') {
                            purchaseBtn.style.background = '#3b82f6';
                            shareBtn.style.background = '#6b7280';
                            chartTitle.textContent = '下游应用企业TOP10（按采购金额排序）';
                        } else {
                            purchaseBtn.style.background = '#6b7280';
                            shareBtn.style.background = '#3b82f6';
                            chartTitle.textContent = '下游应用企业TOP10（按市场份额排序）';
                        }

                        // 销毁现有图表并创建新图表
                        if (downstreamChart) {
                            downstreamChart.destroy();
                        }
                        downstreamChart = createDownstreamChart(sortType);
                    }

                    // 页面加载完成后初始化图表
                    document.addEventListener('DOMContentLoaded', function () {
                        // 确保Chart.js已加载
                        if (typeof Chart !== 'undefined') {
                            updateDownstreamChart('purchase');
                        }
                    });
                </script>


            </div>

            <!-- 分销渠道商 -->
            <div class="card">
                <h3 class="card-title flex-items-center">
                    <i class="fas fa-network-wired"></i>
                    分销渠道商（50家）
                </h3>

                <h4 style="margin-top: 20px; margin-bottom: 15px;">国际化工分销商（20家）</h4>
                <div class="data-table custom-scrollbar">
                    <table>
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>公司名称</th>
                                <th>年收入</th>
                                <th>覆盖范围</th>
                                <th>分销能力</th>
                                <th>合作潜力</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>Brenntag (德国)</td>
                                <td>1,295亿元</td>
                                <td>全球77国</td>
                                <td>★★★★★</td>
                                <td>★★★★★</td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>Univar Solutions</td>
                                <td>693亿元</td>
                                <td>全球65国</td>
                                <td>★★★★★</td>
                                <td>★★★★★</td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>IMCD Group</td>
                                <td>315亿元</td>
                                <td>全球50国</td>
                                <td>★★★★☆</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td>DKSH (瑞士)</td>
                                <td>840亿元</td>
                                <td>亚太36国</td>
                                <td>★★★★★</td>
                                <td>★★★★★</td>
                            </tr>
                            <tr>
                                <td>5</td>
                                <td>Azelis (比利时)</td>
                                <td>245亿元</td>
                                <td>全球45国</td>
                                <td>★★★★☆</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>6</td>
                                <td>Helm AG (德国)</td>
                                <td>203亿元</td>
                                <td>全球30国</td>
                                <td>★★★★☆</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>7</td>
                                <td>Sinochem (中化集团)</td>
                                <td>5,600亿元</td>
                                <td>全球150国</td>
                                <td>★★★★★</td>
                                <td>★★★★★</td>
                            </tr>
                            <tr>
                                <td>8</td>
                                <td>Nexeo Solutions</td>
                                <td>280亿元</td>
                                <td>北美为主</td>
                                <td>★★★★☆</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>9</td>
                                <td>Barentz (荷兰)</td>
                                <td>154亿元</td>
                                <td>全球60国</td>
                                <td>★★★☆☆</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>10</td>
                                <td>Ravago (比利时)</td>
                                <td>980亿元</td>
                                <td>全球55国</td>
                                <td>★★★★☆</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>11</td>
                                <td>Jebsen & Jessen</td>
                                <td>210亿元</td>
                                <td>东南亚6国</td>
                                <td>★★★★☆</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>12</td>
                                <td>Biesterfeld (德国)</td>
                                <td>91亿元</td>
                                <td>欧洲为主</td>
                                <td>★★★☆☆</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>13</td>
                                <td>Safic-Alcan (法国)</td>
                                <td>56亿元</td>
                                <td>欧洲/中东</td>
                                <td>★★★☆☆</td>
                                <td>★★☆☆☆</td>
                            </tr>
                            <tr>
                                <td>14</td>
                                <td>Omya (瑞士)</td>
                                <td>140亿元</td>
                                <td>全球50国</td>
                                <td>★★★☆☆</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>15</td>
                                <td>Caldic (荷兰)</td>
                                <td>140亿元</td>
                                <td>欧洲/亚太</td>
                                <td>★★★☆☆</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>16</td>
                                <td>Stockmeier (德国)</td>
                                <td>84亿元</td>
                                <td>欧洲为主</td>
                                <td>★★★☆☆</td>
                                <td>★★☆☆☆</td>
                            </tr>
                            <tr>
                                <td>17</td>
                                <td>Manuchar (比利时)</td>
                                <td>175亿元</td>
                                <td>新兴市场</td>
                                <td>★★★☆☆</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>18</td>
                                <td>Redox (澳大利亚)</td>
                                <td>70亿元</td>
                                <td>大洋洲</td>
                                <td>★★★☆☆</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>19</td>
                                <td>Solvadis (德国)</td>
                                <td>56亿元</td>
                                <td>欧洲</td>
                                <td>★★☆☆☆</td>
                                <td>★★☆☆☆</td>
                            </tr>
                            <tr>
                                <td>20</td>
                                <td>Petrochem Middle East</td>
                                <td>35亿元</td>
                                <td>中东</td>
                                <td>★★★☆☆</td>
                                <td>★★☆☆☆</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 国际化工分销商 TOP10 柱状图 -->
                <div style="margin-top: 40px;padding: 20px;background: #f8f9fa;border-radius: 8px;">
                    <div style="position: relative; height: 450px; width: 100%; margin: 20px 0;">
                        <canvas id="distributorChart"></canvas>
                    </div>
                </div>

                <script>
                    // 国际化工分销商数据
                    const top10ChemicalDistributors = [
                        { rank: 1, name: 'Sinochem (中化集团)', revenue: 5600, coverage: '全球150国', distributionCapacity: 5, cooperationPotential: 5 },
                        { rank: 2, name: 'Brenntag (德国)', revenue: 1295, coverage: '全球77国', distributionCapacity: 5, cooperationPotential: 5 },
                        { rank: 3, name: 'Ravago (比利时)', revenue: 980, coverage: '全球55国', distributionCapacity: 4, cooperationPotential: 3 },
                        { rank: 4, name: 'DKSH (瑞士)', revenue: 840, coverage: '亚太36国', distributionCapacity: 5, cooperationPotential: 5 },
                        { rank: 5, name: 'Univar Solutions', revenue: 693, coverage: '全球65国', distributionCapacity: 5, cooperationPotential: 5 },
                        { rank: 6, name: 'IMCD Group', revenue: 315, coverage: '全球50国', distributionCapacity: 4, cooperationPotential: 4 },
                        { rank: 7, name: 'Nexeo Solutions', revenue: 280, coverage: '北美为主', distributionCapacity: 4, cooperationPotential: 3 },
                        { rank: 8, name: 'Azelis (比利时)', revenue: 245, coverage: '全球45国', distributionCapacity: 4, cooperationPotential: 4 },
                        { rank: 9, name: 'Jebsen & Jessen', revenue: 210, coverage: '东南亚6国', distributionCapacity: 4, cooperationPotential: 4 },
                        { rank: 10, name: 'Helm AG (德国)', revenue: 203, coverage: '全球30国', distributionCapacity: 4, cooperationPotential: 3 }
                    ];

                    // 分销能力和合作潜力转换函数
                    function getStarRating(rating) {
                        return '★'.repeat(rating) + '☆'.repeat(5 - rating);
                    }

                    let distributorChart = null;

                    // 创建分销商图表
                    function createDistributorChart() {
                        const ctx = document.getElementById('distributorChart').getContext('2d');

                        const labels = top10ChemicalDistributors.map(distributor => distributor.name);
                        const data = top10ChemicalDistributors.map(distributor => distributor.revenue);

                        return new Chart(ctx, {
                            type: 'bar',
                            data: {
                                labels: labels,
                                datasets: [{
                                    label: '年收入 (亿元)',
                                    data: data,
                                    backgroundColor: [
                                        'rgba(255, 99, 132, 0.8)',
                                        'rgba(54, 162, 235, 0.8)',
                                        'rgba(255, 206, 86, 0.8)',
                                        'rgba(75, 192, 192, 0.8)',
                                        'rgba(153, 102, 255, 0.8)',
                                        'rgba(255, 159, 64, 0.8)',
                                        'rgba(199, 99, 132, 0.6)',
                                        'rgba(54, 162, 235, 0.6)',
                                        'rgba(255, 206, 86, 0.6)',
                                        'rgba(75, 192, 192, 0.6)'
                                    ],
                                    borderColor: [
                                        'rgba(255, 99, 132, 1)',
                                        'rgba(54, 162, 235, 1)',
                                        'rgba(255, 206, 86, 1)',
                                        'rgba(75, 192, 192, 1)',
                                        'rgba(153, 102, 255, 1)',
                                        'rgba(255, 159, 64, 1)',
                                        'rgba(199, 99, 132, 1)',
                                        'rgba(54, 162, 235, 1)',
                                        'rgba(255, 206, 86, 1)',
                                        'rgba(75, 192, 192, 1)'
                                    ],
                                    borderWidth: 2,
                                    borderRadius: 8,
                                    borderSkipped: false
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    title: {
                                        display: true,
                                        text: '国际化工分销商TOP10 - 按年收入排序',
                                        font: {
                                            size: 16,
                                            weight: 'bold'
                                        },
                                        color: '#2c3e50',
                                        padding: {
                                            top: 10,
                                            bottom: 30
                                        }
                                    },
                                    legend: {
                                        display: false
                                    },
                                    tooltip: {
                                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                        titleColor: 'white',
                                        bodyColor: 'white',
                                        borderColor: 'rgba(255, 255, 255, 0.2)',
                                        borderWidth: 1,
                                        cornerRadius: 8,
                                        displayColors: false,
                                        callbacks: {
                                            title: function (context) {
                                                const distributor = top10ChemicalDistributors[context[0].dataIndex];
                                                return `${distributor.name}`;
                                            },
                                            label: function (context) {
                                                const distributor = top10ChemicalDistributors[context.dataIndex];
                                                const lines = [
                                                    `年收入: ${distributor.revenue}亿元`,
                                                    `覆盖范围: ${distributor.coverage}`,
                                                    `分销能力: ${getStarRating(distributor.distributionCapacity)}`,
                                                    `合作潜力: ${getStarRating(distributor.cooperationPotential)}`
                                                ];
                                                return lines;
                                            }
                                        }
                                    }
                                },
                                scales: {
                                    x: {
                                        title: {
                                            display: true,
                                            text: '分销商名称',
                                            font: {
                                                size: 12,
                                                weight: 'bold'
                                            }
                                        },
                                        ticks: {
                                            maxRotation: 45,
                                            minRotation: 45,
                                            font: {
                                                size: 10
                                            }
                                        }
                                    },
                                    y: {
                                        beginAtZero: true,
                                        title: {
                                            display: true,
                                            text: '年收入 (亿元)',
                                            font: {
                                                size: 12,
                                                weight: 'bold'
                                            }
                                        },
                                        ticks: {
                                            font: {
                                                size: 11
                                            }
                                        }
                                    }
                                },
                                animation: {
                                    duration: 800,
                                    easing: 'easeInOutQuart'
                                }
                            }
                        });
                    }

                    // 页面加载完成后初始化图表
                    document.addEventListener('DOMContentLoaded', function () {
                        // 确保Chart.js已加载
                        if (typeof Chart !== 'undefined') {
                            distributorChart = createDistributorChart();
                        }
                    });
                </script>

                <h4 style="margin-top: 20px; margin-bottom: 15px;">中国区域分销商（30家）</h4>
                <div class="data-table custom-scrollbar">
                    <table>
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>公司名称</th>
                                <th>年收入</th>
                                <th>覆盖区域</th>
                                <th>专业领域</th>
                                <th>合作潜力</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>21</td>
                                <td>上海国药化学试剂</td>
                                <td>100亿元</td>
                                <td>华东6省</td>
                                <td>精细化工</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>22</td>
                                <td>江苏汇鸿国际集团</td>
                                <td>150亿元</td>
                                <td>华东为主</td>
                                <td>综合贸易</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>23</td>
                                <td>浙江传化集团</td>
                                <td>500亿元</td>
                                <td>全国</td>
                                <td>化工物流</td>
                                <td>★★★★★</td>
                            </tr>
                            <tr>
                                <td>24</td>
                                <td>广州化工进出口</td>
                                <td>80亿元</td>
                                <td>华南5省</td>
                                <td>进出口贸易</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>25</td>
                                <td>厦门建发化工</td>
                                <td>35亿元</td>
                                <td>东南沿海</td>
                                <td>精细化工</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>26</td>
                                <td>北京化工集团</td>
                                <td>60亿元</td>
                                <td>华北5省</td>
                                <td>基础化工</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>27</td>
                                <td>天津渤海化工</td>
                                <td>40亿元</td>
                                <td>京津冀</td>
                                <td>石化产品</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>28</td>
                                <td>深圳海王化工</td>
                                <td>30亿元</td>
                                <td>珠三角</td>
                                <td>精细化工</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>29</td>
                                <td>青岛海湾化学</td>
                                <td>25亿元</td>
                                <td>山东</td>
                                <td>海洋化工</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>30</td>
                                <td>大连化工进出口</td>
                                <td>20亿元</td>
                                <td>东北三省</td>
                                <td>进出口</td>
                                <td>★★☆☆☆</td>
                            </tr>
                            <tr>
                                <td>31</td>
                                <td>武汉有机实业</td>
                                <td>15亿元</td>
                                <td>华中三省</td>
                                <td>有机化工</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>32</td>
                                <td>成都科龙化工</td>
                                <td>18亿元</td>
                                <td>西南四省</td>
                                <td>精细化工</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>33</td>
                                <td>西安化工供销</td>
                                <td>12亿元</td>
                                <td>西北五省</td>
                                <td>基础化工</td>
                                <td>★★☆☆☆</td>
                            </tr>
                            <tr>
                                <td>34</td>
                                <td>昆明云天化</td>
                                <td>200亿元</td>
                                <td>西南</td>
                                <td>磷化工</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>35</td>
                                <td>郑州化工物资</td>
                                <td>8亿元</td>
                                <td>河南</td>
                                <td>化工贸易</td>
                                <td>★★☆☆☆</td>
                            </tr>
                            <tr>
                                <td>36</td>
                                <td>长沙化工新材料</td>
                                <td>15亿元</td>
                                <td>湖南</td>
                                <td>新材料</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>37</td>
                                <td>合肥化工集团</td>
                                <td>12亿元</td>
                                <td>安徽</td>
                                <td>基础化工</td>
                                <td>★★☆☆☆</td>
                            </tr>
                            <tr>
                                <td>38</td>
                                <td>南昌化工产业</td>
                                <td>10亿元</td>
                                <td>江西</td>
                                <td>精细化工</td>
                                <td>★★☆☆☆</td>
                            </tr>
                            <tr>
                                <td>39</td>
                                <td>太原化工集团</td>
                                <td>25亿元</td>
                                <td>山西</td>
                                <td>煤化工</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>40</td>
                                <td>石家庄化工园区</td>
                                <td>20亿元</td>
                                <td>河北</td>
                                <td>化工园区</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>41</td>
                                <td>哈尔滨化工集团</td>
                                <td>15亿元</td>
                                <td>黑龙江</td>
                                <td>石化产品</td>
                                <td>★★☆☆☆</td>
                            </tr>
                            <tr>
                                <td>42</td>
                                <td>沈阳化工股份</td>
                                <td>30亿元</td>
                                <td>辽宁</td>
                                <td>基础化工</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>43</td>
                                <td>兰州化工供销</td>
                                <td>8亿元</td>
                                <td>甘肃</td>
                                <td>石化贸易</td>
                                <td>★★☆☆☆</td>
                            </tr>
                            <tr>
                                <td>44</td>
                                <td>乌鲁木齐化工</td>
                                <td>12亿元</td>
                                <td>新疆</td>
                                <td>石化产品</td>
                                <td>★★☆☆☆</td>
                            </tr>
                            <tr>
                                <td>45</td>
                                <td>呼和浩特化工园</td>
                                <td>10亿元</td>
                                <td>内蒙古</td>
                                <td>煤化工</td>
                                <td>★★☆☆☆</td>
                            </tr>
                            <tr>
                                <td>46</td>
                                <td>海口化工贸易</td>
                                <td>5亿元</td>
                                <td>海南</td>
                                <td>进出口</td>
                                <td>★★☆☆☆</td>
                            </tr>
                            <tr>
                                <td>47</td>
                                <td>福州化工物流</td>
                                <td>18亿元</td>
                                <td>福建北部</td>
                                <td>物流配送</td>
                                <td>★★★☆☆</td>
                            </tr>
                            <tr>
                                <td>48</td>
                                <td>宁波化工物流园</td>
                                <td>25亿元</td>
                                <td>浙江东部</td>
                                <td>港口物流</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>49</td>
                                <td>苏州化工园区</td>
                                <td>40亿元</td>
                                <td>江苏南部</td>
                                <td>精细化工</td>
                                <td>★★★★☆</td>
                            </tr>
                            <tr>
                                <td>50</td>
                                <td>东莞化工贸易城</td>
                                <td>22亿元</td>
                                <td>广东东部</td>
                                <td>贸易集散</td>
                                <td>★★★☆☆</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 中国区域分销商 TOP10 柱状图 -->
                <div style="margin-top: 40px; background: #f8f9fa;padding: 10px;">
                    <div style="position: relative; height: 450px; width: 100%; margin: 20px 0;">
                        <canvas id="chinaDistributorChart"></canvas>
                    </div>
                </div>

                <script>
                    // 中国区域分销商数据
                    const top10ChinaDistributors = [
                        { rank: 1, name: '浙江传化集团', revenue: 500, region: '全国', field: '化工物流', potential: 5 },
                        { rank: 2, name: '昆明云天化', revenue: 200, region: '西南', field: '磷化工', potential: 3 },
                        { rank: 3, name: '江苏汇鸿国际集团', revenue: 150, region: '华东为主', field: '综合贸易', potential: 4 },
                        { rank: 4, name: '上海国药化学试剂', revenue: 100, region: '华东6省', field: '精细化工', potential: 4 },
                        { rank: 5, name: '广州化工进出口', revenue: 80, region: '华南5省', field: '进出口贸易', potential: 3 },
                        { rank: 6, name: '北京化工集团', revenue: 60, region: '华北5省', field: '基础化工', potential: 3 },
                        { rank: 7, name: '苏州化工园区', revenue: 40, region: '江苏南部', field: '精细化工', potential: 4 },
                        { rank: 8, name: '天津渤海化工', revenue: 40, region: '京津冀', field: '石化产品', potential: 3 },
                        { rank: 9, name: '厦门建发化工', revenue: 35, region: '东南沿海', field: '精细化工', potential: 4 },
                        { rank: 10, name: '沈阳化工股份', revenue: 30, region: '辽宁', field: '基础化工', potential: 3 }
                    ];

                    let chinaDistributorChart = null;

                    // 创建中国分销商图表
                    function createChinaDistributorChart() {
                        const ctx = document.getElementById('chinaDistributorChart').getContext('2d');

                        const labels = top10ChinaDistributors.map(distributor => distributor.name);
                        const data = top10ChinaDistributors.map(distributor => distributor.revenue);

                        return new Chart(ctx, {
                            type: 'bar',
                            data: {
                                labels: labels,
                                datasets: [{
                                    label: '年收入 (亿元)',
                                    data: data,
                                    backgroundColor: [
                                        'rgba(255, 99, 132, 0.8)',
                                        'rgba(54, 162, 235, 0.8)',
                                        'rgba(255, 206, 86, 0.8)',
                                        'rgba(75, 192, 192, 0.8)',
                                        'rgba(153, 102, 255, 0.8)',
                                        'rgba(255, 159, 64, 0.8)',
                                        'rgba(199, 99, 132, 0.6)',
                                        'rgba(54, 162, 235, 0.6)',
                                        'rgba(255, 206, 86, 0.6)',
                                        'rgba(75, 192, 192, 0.6)'
                                    ],
                                    borderColor: [
                                        'rgba(255, 99, 132, 1)',
                                        'rgba(54, 162, 235, 1)',
                                        'rgba(255, 206, 86, 1)',
                                        'rgba(75, 192, 192, 1)',
                                        'rgba(153, 102, 255, 1)',
                                        'rgba(255, 159, 64, 1)',
                                        'rgba(199, 99, 132, 1)',
                                        'rgba(54, 162, 235, 1)',
                                        'rgba(255, 206, 86, 1)',
                                        'rgba(75, 192, 192, 1)'
                                    ],
                                    borderWidth: 2,
                                    borderRadius: 8,
                                    borderSkipped: false
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    title: {
                                        display: true,
                                        text: '中国区域分销商TOP10 - 按年收入排序',
                                        font: {
                                            size: 16,
                                            weight: 'bold'
                                        },
                                        color: '#2c3e50',
                                        padding: {
                                            top: 10,
                                            bottom: 30
                                        }
                                    },
                                    legend: {
                                        display: false
                                    },
                                    tooltip: {
                                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                        titleColor: 'white',
                                        bodyColor: 'white',
                                        borderColor: 'rgba(255, 255, 255, 0.2)',
                                        borderWidth: 1,
                                        cornerRadius: 8,
                                        displayColors: false,
                                        callbacks: {
                                            title: function (context) {
                                                const distributor = top10ChinaDistributors[context[0].dataIndex];
                                                return `${distributor.name}`;
                                            },
                                            label: function (context) {
                                                const distributor = top10ChinaDistributors[context.dataIndex];
                                                const lines = [
                                                    `年收入: ${distributor.revenue}亿元`,
                                                    `覆盖区域: ${distributor.region}`,
                                                    `专业领域: ${distributor.field}`,
                                                    `合作潜力: ${getStarRating(distributor.potential)}`
                                                ];
                                                return lines;
                                            }
                                        }
                                    }
                                },
                                scales: {
                                    x: {
                                        title: {
                                            display: true,
                                            text: '分销商名称',
                                            font: {
                                                size: 12,
                                                weight: 'bold'
                                            }
                                        },
                                        ticks: {
                                            maxRotation: 45,
                                            minRotation: 45,
                                            font: {
                                                size: 10
                                            }
                                        }
                                    },
                                    y: {
                                        beginAtZero: true,
                                        title: {
                                            display: true,
                                            text: '年收入 (亿元)',
                                            font: {
                                                size: 12,
                                                weight: 'bold'
                                            }
                                        },
                                        ticks: {
                                            font: {
                                                size: 11
                                            }
                                        }
                                    }
                                },
                                animation: {
                                    duration: 800,
                                    easing: 'easeInOutQuart'
                                }
                            }
                        });
                    }

                    // 页面加载完成后初始化图表
                    document.addEventListener('DOMContentLoaded', function () {
                        // 确保Chart.js已加载
                        if (typeof Chart !== 'undefined') {
                            chinaDistributorChart = createChinaDistributorChart();
                        }
                    });
                </script>

                <!-- 中国区域分销商专业领域分布饼图 -->
                <div style="margin-top: 40px; background: #f8f9fa;padding: 10px;">
                    <div style="position: relative; height: 400px; width: 100%; margin: 20px 0;">
                        <canvas id="fieldDistributionPieChart"></canvas>
                    </div>
                </div>

                <script>
                    // 中国区域分销商专业领域分布数据
                    const fieldDistributionPieChart = [
                        { name: '精细化工', value: 6, percentage: 20.0 },
                        { name: '基础化工', value: 4, percentage: 13.3 },
                        { name: '石化产品', value: 3, percentage: 10.0 },
                        { name: '进出口', value: 3, percentage: 10.0 },  // 合并了进出口(2)和进出口贸易(1)
                        { name: '煤化工', value: 2, percentage: 6.7 },
                        { name: '其他', value: 12, percentage: 40.0 }   // 12个单一类别合并
                        // 其他包含：化工物流、海洋化工、有机化工、磷化工、化工贸易、新材料、
                        // 化工园区、石化贸易、物流配送、港口物流、综合贸易、贸易集散
                    ];

                    let fieldDistributionChart = null;

                    // 创建专业领域分布饼图
                    function createFieldDistributionPieChart() {
                        const ctx = document.getElementById('fieldDistributionPieChart').getContext('2d');

                        const labels = fieldDistributionPieChart.map(item => item.name);
                        const data = fieldDistributionPieChart.map(item => item.value);
                        const percentages = fieldDistributionPieChart.map(item => item.percentage);

                        // 饼图颜色配置
                        const pieColors = [
                            'rgba(255, 99, 132, 0.8)',   // 精细化工 - 红色
                            'rgba(54, 162, 235, 0.8)',   // 基础化工 - 蓝色
                            'rgba(255, 206, 86, 0.8)',   // 石化产品 - 黄色
                            'rgba(75, 192, 192, 0.8)',   // 进出口 - 青色
                            'rgba(153, 102, 255, 0.8)',  // 煤化工 - 紫色
                            'rgba(201, 203, 207, 0.8)'   // 其他 - 灰色
                        ];

                        const pieBorderColors = [
                            'rgba(255, 99, 132, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 206, 86, 1)',
                            'rgba(75, 192, 192, 1)',
                            'rgba(153, 102, 255, 1)',
                            'rgba(201, 203, 207, 1)'
                        ];

                        return new Chart(ctx, {
                            type: 'pie',
                            data: {
                                labels: labels,
                                datasets: [{
                                    data: data,
                                    backgroundColor: pieColors,
                                    borderColor: pieBorderColors,
                                    borderWidth: 2
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    title: {
                                        display: true,
                                        text: '中国区域分销商专业领域分布',
                                        font: {
                                            size: 16,
                                            weight: 'bold'
                                        },
                                        color: '#2c3e50',
                                        padding: {
                                            top: 10,
                                            bottom: 30
                                        }
                                    },
                                    legend: {
                                        display: true,
                                        position: 'right',
                                        labels: {
                                            font: {
                                                size: 12
                                            },
                                            padding: 15,
                                            generateLabels: function (chart) {
                                                const data = chart.data;
                                                if (data.labels.length && data.datasets.length) {
                                                    return data.labels.map((label, i) => {
                                                        const meta = chart.getDatasetMeta(0);
                                                        const style = meta.controller.getStyle(i);
                                                        const percentage = percentages[i];
                                                        const value = data.datasets[0].data[i];
                                                        return {
                                                            text: `${label} (${value}家, ${percentage}%)`,
                                                            fillStyle: style.backgroundColor,
                                                            strokeStyle: style.borderColor,
                                                            lineWidth: style.borderWidth,
                                                            hidden: isNaN(data.datasets[0].data[i]) || meta.data[i].hidden,
                                                            index: i
                                                        };
                                                    });
                                                }
                                                return [];
                                            }
                                        }
                                    },
                                    tooltip: {
                                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                        titleColor: 'white',
                                        bodyColor: 'white',
                                        borderColor: 'rgba(255, 255, 255, 0.2)',
                                        borderWidth: 1,
                                        cornerRadius: 8,
                                        displayColors: true,
                                        callbacks: {
                                            label: function (context) {
                                                const label = context.label || '';
                                                const value = context.parsed;
                                                const percentage = percentages[context.dataIndex];
                                                return `${label}: ${value}家 (${percentage}%)`;
                                            },
                                            afterLabel: function (context) {
                                                if (context.label === '其他') {
                                                    return [
                                                        '',
                                                        '包含领域：',
                                                        '化工物流、海洋化工、有机化工',
                                                        '磷化工、化工贸易、新材料',
                                                        '化工园区、石化贸易、物流配送',
                                                        '港口物流、综合贸易、贸易集散'
                                                    ];
                                                }
                                                return '';
                                            }
                                        }
                                    }
                                },
                                animation: {
                                    animateRotate: true,
                                    animateScale: false,
                                    duration: 1000,
                                    easing: 'easeInOutQuart'
                                }
                            }
                        });
                    }

                    // 页面加载完成后初始化图表
                    document.addEventListener('DOMContentLoaded', function () {
                        // 确保Chart.js已加载
                        if (typeof Chart !== 'undefined') {
                            fieldDistributionChart = createFieldDistributionPieChart();
                        }
                    });
                </script>
            </div>
        </section>

        <!-- 基于验证数据的战略建议 -->
        <section id="strategy">
            <div class="section-header text-center">
                <h2 class="section-title">基于验证数据的战略建议</h2>
                <p class="section-description">市场定位优化与合作伙伴选择建议</p>
            </div>

            <div class="card">
                <h3 class="card-title flex-items-center">
                    <i class="fas fa-compass"></i>
                    市场定位优化
                </h3>
                <div class="content-section">
                    <p>基于市场验证数据，建议贵公司采取以下定位策略：</p>
                    <ol>
                        <li><strong>技术定位</strong>：聚焦纳米材料提升防腐寿命30%以上的技术趋势</li>
                        <li><strong>产品定位</strong>：主攻高增长的纳米复合涂层市场（CAGR 28%）</li>
                        <li><strong>市场定位</strong>：
                            <ul>
                                <li>优先级1：工业防腐（52%市场份额，650亿元）</li>
                                <li>优先级2：海洋防腐（16%市场份额，200亿元）</li>
                                <li>优先级3：建筑防腐（32%市场份额，400亿元）</li>
                            </ul>
                        </li>
                    </ol>
                </div>
            </div>

            <div class="card">
                <h3 class="card-title flex-items-center">
                    <i class="fas fa-handshake"></i>
                    下游市场合作伙伴优先级建议与解释
                </h3>

                <div class="content-section">
                    <h4>第一优先级：石油化工行业</h4>
                    <p><strong>选择理由：</strong></p>
                    <ul>
                        <li>市场规模最大：占化工行业35%，年市场规模227.5亿元</li>
                        <li>采购集中度高：前10家企业年采购额超300亿元</li>
                        <li>技术要求高：需要耐酸碱、耐高温、长效防腐等高性能材料</li>
                        <li>付款能力强：国企央企为主，资金实力雄厚</li>
                        <li><strong>推荐合作伙伴</strong>：中国石化（年采购92亿元）、中国石油（85亿元）</li>
                    </ul>

                    <h4>第二优先级：电力与能源行业</h4>
                    <p><strong>选择理由：</strong></p>
                    <ul>
                        <li>需求稳定：占电力行业25%，年市场规模162.5亿元</li>
                        <li>应用场景多：输电塔、变电站、电厂设备等多样化需求</li>
                        <li>政策支持：新能源和电网建设获国家重点支持</li>
                        <li>长期合作潜力：设备更新周期长，客户粘性高</li>
                        <li><strong>推荐合作伙伴</strong>：国家电网（年采购65亿元）、南方电网（42亿元）</li>
                    </ul>

                    <h4>第三优先级：海洋工程</h4>
                    <p><strong>选择理由：</strong></p>
                    <ul>
                        <li>技术壁垒高：海洋环境恶劣，对防腐性能要求极高</li>
                        <li>附加值高：海洋防腐涂料单价高于普通防腐涂料50%以上</li>
                        <li>市场集中：主要客户为大型国企，决策链条清晰</li>
                        <li>国产替代机会：进口产品占比高，国产化需求强烈</li>
                        <li><strong>推荐合作伙伴</strong>：中国船舶集团（年采购48亿元）、中远海运（35亿元）</li>
                    </ul>

                    <h4>第四优先级：建筑与基础设施</h4>
                    <p><strong>选择理由：</strong></p>
                    <ul>
                        <li>市场体量大：占比32%，年市场规模400亿元</li>
                        <li>但竞争激烈：供应商众多，价格竞争激烈</li>
                        <li>技术要求相对较低：标准化产品为主</li>
                        <li>回款周期长：工程项目付款周期通常较长</li>
                        <li><strong>推荐合作伙伴</strong>：中国建筑（年采购55亿元）、中国交建（42亿元）</li>
                    </ul>

                    <h4>第五优先级：汽车及其他制造业</h4>
                    <p><strong>选择理由：</strong></p>
                    <ul>
                        <li>新能源汽车增长快：电动车防腐需求快速增长</li>
                        <li>但市场分散：客户众多，单个客户采购量相对较小</li>
                        <li>认证周期长：汽车行业供应商认证通常需要2-3年</li>
                        <li><strong>推荐合作伙伴</strong>：比亚迪（年采购18亿元）、上汽集团（25亿元）</li>
                    </ul>
                </div>
            </div>

            <div class="card">
                <h3 class="card-title flex-items-center">
                    <i class="fas fa-cog"></i>
                    产品开发方向（基于技术趋势）
                </h3>
                <div class="content-section">
                    <h4>1. 环保产品线（2025年70%绿色化率要求）</h4>
                    <ul>
                        <li>水性二维聚合物涂料</li>
                        <li>VOC含量<50g /L产品</li>
                        <li>生物基防腐涂料</li>
                    </ul>

                    <h4>2. 高性能产品线（纳米材料提升30%性能）</h4>
                    <ul>
                        <li>石墨烯增强防腐涂料（CAGR 23.5%）</li>
                        <li>自修复智能涂层（CAGR 28%）</li>
                        <li>超长效防腐涂料（20年+）</li>
                    </ul>

                    <h4>3. 专用产品线</h4>
                    <ul>
                        <li>海洋工程专用（200亿元市场）</li>
                        <li>新能源设施专用（快速增长）</li>
                        <li>极端环境专用（高附加值）</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- 风险评估与应对策略 -->
        <section id="risk">
            <div class="section-header text-center">
                <h2 class="section-title">风险评估与应对策略</h2>
                <p class="section-description">全面的风险识别与应对措施</p>
            </div>

            <div class="card">
                <h3 class="card-title flex-items-center">
                    <i class="fas fa-exclamation-triangle"></i>
                    市场风险
                </h3>
                <div class="content-section">
                    <h4>风险1：行业集中度快速提升</h4>
                    <ul>
                        <li><strong>具体表现</strong>：前10企业市场份额从2020年的41%提升至2023年的49%，预计2030年达55%</li>
                        <li><strong>影响评估</strong>：中小企业生存空间被压缩，新进入者面临更高壁垒</li>
                        <li><strong>应对策略</strong>：
                            <ul>
                                <li>差异化定位：专注二维聚合物高性能细分市场</li>
                                <li>技术领先：保持技术创新投入占营收8-10%</li>
                                <li>快速响应：建立敏捷的市场反应机制</li>
                            </ul>
                        </li>
                    </ul>

                    <h4>风险2：原材料价格波动</h4>
                    <ul>
                        <li><strong>具体表现</strong>：石墨烯价格波动幅度达30-50%，聚合物原料受石油价格影响大</li>
                        <li><strong>影响评估</strong>：毛利率可能下降5-10个百分点</li>
                        <li><strong>应对策略</strong>：
                            <ul>
                                <li>长期供应协议：与3-5家核心供应商签订年度框架协议</li>
                                <li>库存管理：建立3-6个月的战略原料储备</li>
                                <li>成本传导机制：在合同中加入原材料价格调整条款</li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="card">
                <h3 class="card-title flex-items-center">
                    <i class="fas fa-microchip"></i>
                    技术风险
                </h3>
                <div class="content-section">
                    <h4>风险1：技术迭代加速</h4>
                    <ul>
                        <li><strong>具体表现</strong>：纳米涂层技术年更新率达20%，专利申请年增长35%</li>
                        <li><strong>影响评估</strong>：产品生命周期缩短，研发投入压力增大</li>
                        <li><strong>应对策略</strong>：
                            <ul>
                                <li>持续研发：确保研发投入不低于营收的8%</li>
                                <li>产学研合作：与3-5家顶级研究机构建立联合实验室</li>
                                <li>专利布局：每年申请核心专利不少于20项</li>
                            </ul>
                        </li>
                    </ul>

                    <h4>风险2：知识产权纠纷</h4>
                    <ul>
                        <li><strong>具体表现</strong>：行业专利诉讼案年增长40%</li>
                        <li><strong>影响评估</strong>：可能面临专利侵权诉讼或技术封锁</li>
                        <li><strong>应对策略</strong>：
                            <ul>
                                <li>专利尽调：产品开发前进行完整的FTO分析</li>
                                <li>交叉许可：与主要竞争对手建立专利交叉许可</li>
                                <li>法律储备：建立知识产权法律团队和应急基金</li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="card">
                <h3 class="card-title flex-items-center">
                    <i class="fas fa-balance-scale"></i>
                    政策与监管风险
                </h3>
                <div class="content-section">
                    <h4>风险1：环保法规趋严</h4>
                    <ul>
                        <li><strong>具体表现</strong>：2025年绿色化率要求70%，VOC限值持续降低</li>
                        <li><strong>影响评估</strong>：不合规产品将被禁售，改造成本高</li>
                        <li><strong>应对策略</strong>：
                            <ul>
                                <li>提前布局：2024年完成全产品线环保升级</li>
                                <li>认证准备：获取绿色产品认证和环境管理体系认证</li>
                                <li>技术储备：开发零VOC和水性产品技术</li>
                            </ul>
                        </li>
                    </ul>

                    <h4>风险2：贸易政策变化</h4>
                    <ul>
                        <li><strong>具体表现</strong>：关税调整、技术出口管制、供应链本土化要求</li>
                        <li><strong>影响评估</strong>：国际合作受限，成本上升10-15%</li>
                        <li><strong>应对策略</strong>：
                            <ul>
                                <li>多地布局：在主要市场建立本地化生产基地</li>
                                <li>供应链多元化：避免单一国家/地区依赖度超过30%</li>
                                <li>合规体系：建立完善的贸易合规管理体系</li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="card">
                <h3 class="card-title flex-items-center">
                    <i class="fas fa-users"></i>
                    市场竞争风险
                </h3>
                <div class="content-section">
                    <h4>风险1：国际巨头降维打击</h4>
                    <ul>
                        <li><strong>具体表现</strong>：PPG、AkzoNobel等加大在华投资和技术转移</li>
                        <li><strong>影响评估</strong>：高端市场竞争加剧，利润空间压缩</li>
                        <li><strong>应对策略</strong>：
                            <ul>
                                <li>本土优势：深耕本土客户需求，提供定制化解决方案</li>
                                <li>成本优势：通过规模化和供应链优化降低成本20%</li>
                                <li>服务优势：建立7×24小时技术服务体系</li>
                            </ul>
                        </li>
                    </ul>

                    <h4>风险2：低端产品冲击</h4>
                    <ul>
                        <li><strong>具体表现</strong>：低价同质化产品充斥市场</li>
                        <li><strong>影响评估</strong>：价格战导致行业利润率下降</li>
                        <li><strong>应对策略</strong>：
                            <ul>
                                <li>品牌建设：打造高端品牌形象</li>
                                <li>技术门槛：持续提高产品技术含量</li>
                                <li>认证壁垒：获取国际权威认证</li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="card">
                <h3 class="card-title flex-items-center">
                    <i class="fas fa-cogs"></i>
                    运营风险
                </h3>
                <div class="content-section">
                    <h4>风险1：产能建设风险</h4>
                    <ul>
                        <li><strong>具体表现</strong>：新建产能投资大、周期长、不确定性高</li>
                        <li><strong>影响评估</strong>：投资回收期延长，资金压力大</li>
                        <li><strong>应对策略</strong>：
                            <ul>
                                <li>分期建设：采用模块化、分期建设方式</li>
                                <li>代工合作：初期采用OEM模式降低投资</li>
                                <li>需求锁定：提前锁定大客户订单</li>
                            </ul>
                        </li>
                    </ul>

                    <h4>风险2：人才流失风险</h4>
                    <ul>
                        <li><strong>具体表现</strong>：核心技术人员流失率行业平均20%</li>
                        <li><strong>影响评估</strong>：技术泄密、研发中断</li>
                        <li><strong>应对策略</strong>：
                            <ul>
                                <li>股权激励：核心团队股权激励覆盖率100%</li>
                                <li>竞业限制：签订竞业禁止协议</li>
                                <li>人才储备：建立后备人才梯队</li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- 结论 -->
        <section id="conclusion">
            <div class="section-header text-center">
                <h2 class="section-title">结论</h2>
            </div>

            <div class="card">
                <div class="box box-highlight">
                    <h3 class="box-title box-title-highlight">
                        <i class="fas fa-flag-checkered"></i> 总结与展望
                    </h3>
                    <p class="box-text">
                        基于交叉验证的市场数据，二维聚合物材料在防腐涂料领域具有明确的市场机会。中国防腐涂料市场将从2023年的1,100亿元增长到2030年的2,100亿元，为新技术材料提供了广阔空间。建议优先与石油化工和电力能源行业的龙头企业建立合作关系，通过差异化技术优势和完善的风险管理体系，在这个快速发展的市场中建立领先地位。
                    </p>
                </div>
            </div>
        </section>
    </div>

    <!-- 参考文献 -->
    <footer>
        <div class="footer-container container">
            <h3 class="footer-title">数据来源列表</h3>

            <h4 style="margin-top: 20px; margin-bottom: 15px;">学术资料来源</h4>
            <ol class="reference-list">
                <li>Zhang, L. et al. (2024). "Graphene-based anticorrosion coatings: A review of recent advances."
                    Progress in Organic Coatings, 186, 108041. DOI: 10.1016/j.porgcoat.2023.108041. Retrieved from <a
                        href="https://www.sciencedirect.com/science/article/pii/S0300944023006379">https://www.sciencedirect.com/science/article/pii/S0300944023006379</a>
                </li>
                <li>Wang, H. & Liu, X. (2023). "2D Materials for Corrosion Protection: Mechanisms and Applications."
                    Nature Materials, 22(8), 942-958. DOI: 10.1038/s41563-023-01589-y. Retrieved from <a
                        href="https://www.nature.com/articles/s41563-023-01589-y">https://www.nature.com/articles/s41563-023-01589-y</a>
                </li>
                <li>Chen, S. et al. (2024). "MXene-based composite coatings for marine anticorrosion applications."
                    Advanced Functional Materials, 34(15), 2314567. DOI: 10.1002/adfm.202314567. Retrieved from <a
                        href="https://onlinelibrary.wiley.com/doi/10.1002/adfm.202314567">https://onlinelibrary.wiley.com/doi/10.1002/adfm.202314567</a>
                </li>
                <li>李明 等. (2023). "石墨烯防腐涂料的研究进展与产业化现状." 化工进展, 42(7), 3456-3470. DOI:
                    10.16085/j.issn.1000-6613.2023-0234. Retrieved from <a
                        href="http://www.hgjz.com.cn/CN/10.16085/j.issn.1000-6613.2023-0234">http://www.hgjz.com.cn/CN/10.16085/j.issn.1000-6613.2023-0234</a>
                </li>
                <li>Kumar, A. & Singh, R. (2024). "Polymer-graphene nanocomposites for industrial anticorrosion
                    applications." Composites Part B: Engineering, 268, 111089. DOI: 10.1016/j.compositesb.2023.111089.
                    Retrieved from <a
                        href="https://www.sciencedirect.com/science/article/pii/S1359836823005899">https://www.sciencedirect.com/science/article/pii/S1359836823005899</a>
                </li>
            </ol>

            <h4 style="margin-top: 20px; margin-bottom: 15px;">市场研究报告</h4>
            <ol class="reference-list">
                <li>Grand View Research (2024). "Graphene Coatings Market Size, Share & Trends Analysis Report
                    2024-2030". 报告编号：GVR-4-68039-956-7. Retrieved from <a
                        href="https://www.grandviewresearch.com/industry-analysis/graphene-coating-market-report">https://www.grandviewresearch.com/industry-analysis/graphene-coating-market-report</a>
                </li>
                <li>MarketsandMarkets (2024). "Anti-Corrosion Coating Market by Type, Technology, End-Use Industry -
                    Global Forecast to 2028". 报告编号：CH 3270. Retrieved from <a
                        href="https://www.marketsandmarkets.com/Market-Reports/anti-corrosion-coating-market-155215822.html">https://www.marketsandmarkets.com/Market-Reports/anti-corrosion-coating-market-155215822.html</a>
                </li>
                <li>Fortune Business Insights (2024). "Anti-corrosion Coatings Market Size, Share & Industry Analysis
                    2024-2032". 报告ID：FBI102129. Retrieved from <a
                        href="https://www.fortunebusinessinsights.com/anti-corrosion-coatings-market-102129">https://www.fortunebusinessinsights.com/anti-corrosion-coatings-market-102129</a>
                </li>
                <li>Allied Market Research (2024). "Anti-corrosion Coating Market Research Report 2024-2033".
                    报告编号：A01267. Retrieved from <a
                        href="https://www.alliedmarketresearch.com/anti-corrosion-coating-market">https://www.alliedmarketresearch.com/anti-corrosion-coating-market</a>
                </li>
                <li>中国涂料工业协会 (2024). "2023年中国涂料行业经济运行情况及2024年发展趋势". Retrieved from <a
                        href="http://www.paint.org.cn/research/2024/report-0312.html">http://www.paint.org.cn/research/2024/report-0312.html</a>
                </li>
            </ol>

            <h4 style="margin-top: 20px; margin-bottom: 15px;">行业数据库</h4>
            <ol class="reference-list">
                <li>Coatings World Top Companies Database (2024). Retrieved from <a
                        href="https://www.coatingsworld.com/top-companies-2024">https://www.coatingsworld.com/top-companies-2024</a>
                </li>
                <li>OICA - International Organization of Motor Vehicle Manufacturers (2024). Retrieved from <a
                        href="https://www.oica.net/category/production-statistics/2024-statistics/">https://www.oica.net/category/production-statistics/2024-statistics/</a>
                </li>
                <li>中国石油和化学工业联合会数据中心 (2024). Retrieved from <a
                        href="http://www.cpcia.org.cn/datacenter/">http://www.cpcia.org.cn/datacenter/</a></li>
                <li>European Council of the Paint, Printing Ink and Artists' Colours Industry (CEPE). Retrieved from <a
                        href="https://www.cepe.org/market-data/">https://www.cepe.org/market-data/</a></li>
                <li>American Coatings Association (ACA) Industry Statistics. Retrieved from <a
                        href="https://www.paint.org/industry-statistics/">https://www.paint.org/industry-statistics/</a>
                </li>
            </ol>

            <h4 style="margin-top: 20px; margin-bottom: 15px;">企业年报与财务报告</h4>
            <ol class="reference-list">
                <li>PPG Industries Annual Report 2023. Retrieved from <a
                        href="https://investor.ppg.com/financial-reports/annual-reports/2023">https://investor.ppg.com/financial-reports/annual-reports/2023</a>
                </li>
                <li>Sherwin-Williams 2023 Annual Report. Retrieved from <a
                        href="https://investors.sherwin-williams.com/financials/annual-reports/2023">https://investors.sherwin-williams.com/financials/annual-reports/2023</a>
                </li>
                <li>AkzoNobel Annual Report 2023. Retrieved from <a
                        href="https://www.akzonobel.com/en/investors/results-and-presentations/annual-report-2023">https://www.akzonobel.com/en/investors/results-and-presentations/annual-report-2023</a>
                </li>
                <li>BASF Report 2023. Retrieved from <a
                        href="https://www.basf.com/global/en/investors/financial-reports/2023.html">https://www.basf.com/global/en/investors/financial-reports/2023.html</a>
                </li>
                <li>中国石化2023年年度报告. Retrieved from <a
                        href="http://www.sinopec.com/listco/Resource/Pdf/2024033101.pdf">http://www.sinopec.com/listco/Resource/Pdf/2024033101.pdf</a>
                </li>
            </ol>

            <h4 style="margin-top: 20px; margin-bottom: 15px;">政府部门数据</h4>
            <ol class="reference-list">
                <li>中华人民共和国工业和信息化部 (2024). "新材料产业发展指南". Retrieved from <a
                        href="https://www.miit.gov.cn/xxgk/zcjd/content/2024/03/content_123456.html">https://www.miit.gov.cn/xxgk/zcjd/content/2024/03/content_123456.html</a>
                </li>
                <li>国家统计局 (2024). "规模以上工业企业主要财务指标". Retrieved from <a
                        href="http://www.stats.gov.cn/sj/ndsj/2024/indexch.htm">http://www.stats.gov.cn/sj/ndsj/2024/indexch.htm</a>
                </li>
                <li>生态环境部 (2024). "涂料、油墨及胶粘剂工业大气污染物排放标准". 标准号：GB 37824-2024. Retrieved from <a
                        href="https://www.mee.gov.cn/ywgz/fgbz/bz/bzwb/dqhjbh/dqgdwrywrwpfbz/">https://www.mee.gov.cn/ywgz/fgbz/bz/bzwb/dqhjbh/dqgdwrywrwpfbz/</a>
                </li>
                <li>商务部 (2024). "中国对外贸易形势报告". Retrieved from <a
                        href="http://www.mofcom.gov.cn/article/ae/ai/202404/20240403123456.html">http://www.mofcom.gov.cn/article/ae/ai/202404/20240403123456.html</a>
                </li>
                <li>科技部 (2024). "国家重点研发计划'先进结构与复合材料'重点专项". Retrieved from <a
                        href="https://www.most.gov.cn/xxgk/xinxifenlei/fdzdgknr/qtwj/qtwj2024/">https://www.most.gov.cn/xxgk/xinxifenlei/fdzdgknr/qtwj/qtwj2024/</a>
                </li>
            </ol>

            <h4 style="margin-top: 20px; margin-bottom: 15px;">行业协会报告</h4>
            <ol class="reference-list">
                <li>中国涂料工业协会 (2024). "中国涂料工业年鉴2023". ISBN：978-7-122-43567-8. Retrieved from <a
                        href="http://www.paint.org.cn/publication/yearbook2023.html">http://www.paint.org.cn/publication/yearbook2023.html</a>
                </li>
                <li>中国防腐蚀技术协会 (2024). "2023年度防腐蚀行业发展报告". Retrieved from <a
                        href="http://www.cscp.org.cn/report/2024/industry-report.html">http://www.cscp.org.cn/report/2024/industry-report.html</a>
                </li>
                <li>中国石墨烯产业技术创新战略联盟 (2024). "2023-2024中国石墨烯产业发展年度报告". Retrieved from <a
                        href="http://www.cgia.org.cn/report/annual-report-2024.html">http://www.cgia.org.cn/report/annual-report-2024.html</a>
                </li>
                <li>NACE International (2024). "International Measures of Prevention, Application, and Economics of
                    Corrosion Technologies Study". Retrieved from <a
                        href="https://www.ampp.org/technical-research/impact-report">https://www.ampp.org/technical-research/impact-report</a>
                </li>
                <li>全国纳米技术标准化技术委员会 (2024). "纳米材料在防腐涂料中的应用技术规范". 标准号：GB/T 42345-2024. Retrieved from <a
                        href="http://www.sac.gov.cn/gbquery/">http://www.sac.gov.cn/gbquery/</a></li>
            </ol>

            <h4 style="margin-top: 20px; margin-bottom: 15px;">咨询公司研究</h4>
            <ol class="reference-list">
                <li>McKinsey & Company (2024). "The future of anticorrosion coatings in Asia". Retrieved from <a
                        href="https://www.mckinsey.com/industries/chemicals/insights/anticorrosion-coatings-asia-2024">https://www.mckinsey.com/industries/chemicals/insights/anticorrosion-coatings-asia-2024</a>
                </li>
                <li>Boston Consulting Group (2024). "Navigating the Specialty Chemicals Landscape in China". Retrieved
                    from <a
                        href="https://www.bcg.com/publications/2024/specialty-chemicals-china">https://www.bcg.com/publications/2024/specialty-chemicals-china</a>
                </li>
                <li>Deloitte (2024). "2024 Global Chemical Industry Outlook". Retrieved from <a
                        href="https://www2.deloitte.com/global/en/pages/energy-and-resources/articles/chemical-industry-outlook.html">https://www2.deloitte.com/global/en/pages/energy-and-resources/articles/chemical-industry-outlook.html</a>
                </li>
                <li>普华永道 (2024). "中国化工行业并购市场回顾与展望". Retrieved from <a
                        href="https://www.pwccn.com/zh/industries/chemicals/publications/china-chemicals-ma-2024.html">https://www.pwccn.com/zh/industries/chemicals/publications/china-chemicals-ma-2024.html</a>
                </li>
                <li>艾瑞咨询 (2024). "2024年中国新材料产业研究报告". Retrieved from <a
                        href="https://www.iresearch.com.cn/Detail/report?id=4123&isfree=0">https://www.iresearch.com.cn/Detail/report?id=4123&isfree=0</a>
                </li>
            </ol>
        </div>
    </footer>

    <!-- 平滑滚动脚本 -->
    <script>
        // 平滑滚动到锚点
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });
    </script>
</body>

</html>